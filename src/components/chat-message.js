export class HSNMessage extends LitElement {
  static properties = {
    type: { type: String }, // 'user' | 'ai' | 'system'
    content: { type: String },
    timestamp: { type: String },
    isTyping: { type: Boolean }
  };

  static styles = css`
    :host {
      display: block;
      margin: var(--spacing-sm) 0;
      animation: fadeIn var(--anim-fast) ease-out;
    }

    .message {
      padding: var(--spacing-sm);
      border-left: 2px solid var(--color-primary-amber);
      background: rgba(255, 176, 0, 0.05);
    }

    .message.ai {
      border-left-color: var(--color-neon-cyan);
      background: rgba(0, 255, 255, 0.05);
    }

    .typing-indicator {
      display: inline-block;
      animation: blink var(--anim-fast) infinite;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }
  `;

  render() {
    return html`
      <div class="message ${this.type}">
        <div class="message-content">
          ${this.isTyping ? 
            html`<span class="typing-indicator">▋</span>` : 
            html`${this.content}`
          }
        </div>
        <div class="message-meta">
          ${this.timestamp}
        </div>
      </div>
    `;
  }
}

customElements.define('hsn-message', HSNMessage);