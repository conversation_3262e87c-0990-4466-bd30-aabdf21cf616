import { LitElement, html, css } from 'lit';

export class HSNTerminalApp extends LitElement {
  static properties = {
    theme: { type: String },
    isConnected: { type: Boolean },
    aiProvider: { type: String }
  };

  static styles = css`
    :host {
      /* 繼承現有 CSS 變數 */
      --terminal-bg: var(--bg-primary);
      --terminal-border: var(--color-primary-amber);
      --terminal-text: var(--color-text-primary);
      
      display: block;
      width: 100%;
      height: 100vh;
      background: var(--terminal-bg);
      border: 1px solid rgba(255, 176, 0, 0.2);
      border-radius: var(--border-radius-lg);
      overflow: hidden;
    }

    .terminal-container {
      display: flex;
      flex-direction: column;
      height: 100%;
      font-family: var(--font-mono);
    }

    /* 響應式設計 */
    @media (max-width: 768px) {
      :host {
        border-radius: 0;
        height: 100vh;
      }
    }
  `;

  constructor() {
    super();
    this.theme = 'enhanced-phosphor-green';
    this.isConnected = false;
    this.aiProvider = 'openrouter';
  }

  render() {
    return html`
      <div class="terminal-container" data-theme="${this.theme}">
        <hsn-terminal-header 
          .theme="${this.theme}"
          @theme-change="${this._handleThemeChange}">
        </hsn-terminal-header>
        
        <hsn-chat-container 
          .isConnected="${this.isConnected}"
          .aiProvider="${this.aiProvider}">
        </hsn-chat-container>
        
        <hsn-status-bar 
          .isConnected="${this.isConnected}"
          .aiProvider="${this.aiProvider}">
        </hsn-status-bar>
      </div>
    `;
  }

  _handleThemeChange(e) {
    this.theme = e.detail.theme;
    // 更新全域 CSS 變數
    document.documentElement.setAttribute('data-theme', this.theme);
  }
}

customElements.define('hsn-terminal-app', HSNTerminalApp);