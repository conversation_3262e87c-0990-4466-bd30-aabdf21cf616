import { LitElement, html, css } from 'lit';

class TerminalChat extends LitElement {
  static styles = css`
    :host {
      --terminal-bg: var(--bg-primary);
      --terminal-text: var(--color-primary-amber);
    }
    
    .chat-container {
      background: var(--terminal-bg);
      color: var(--terminal-text);
      /* 重用現有 CSS 變數 */
    }
  `;
  
  render() {
    return html`<div class="chat-container">...</div>`;
  }
}