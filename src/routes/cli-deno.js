import { Hono } from 'hono';

const app = new Hono();

// 🔄 備份提醒: 這是 cli-preview.js 的 Deno 升級版本
// 當前版本: CLI Preview Deno v1.0 (2025-01-04) - Railway 部署修復
// 基於版本: RETRO UI v2.0 全面升級完成版本 (2025-09-03)
//
// 🦕 Deno 升級內容:
// - 替換 Alpine.js → Datastar
// - 替換 InstantDB → Deno KV
// - 保持復古終端美學
// - 保持水母頭像系統
// - 保持 4 套主題系統
// - 優化 Deno 運行時性能

// 🖥️ HSN CLI Deno - 終端式 AI 社交體驗 (Deno 版本)
// 🔧 Railway 測試版本
app.get('/cli-deno-test', (c) => {
	return c.html(`
<!DOCTYPE html>
<html>
<head>
    <title>CLI Deno Test</title>
</head>
<body>
    <h1>CLI Deno Test Route</h1>
    <p>This is a simplified test version to verify Railway deployment.</p>
    <p>Timestamp: ${new Date().toISOString()}</p>
</body>
</html>
    `);
});

app.get('/cli-deno', (c) => {
	return c.html(`<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HSN CLI Deno | 人工智慧時代的新社交媒體</title>

    <!-- Datastar 框架 - 替代 Alpine.js -->
    <script type="module" defer src="https://cdn.jsdelivr.net/npm/@sudodevnull/datastar@latest/dist/datastar.js"></script>

    <!-- Deno KV 客戶端模擬 -->
    <script>
        // 🦕 Deno KV 客戶端適配器
        class DenoKVAdapter {
            constructor() {
                this.baseUrl = '/api/deno/kv';
                this.isConnected = false;
            }

            async connect() {
                try {
                    console.log('🔄 Connecting to Deno KV...');
                    const response = await fetch(\`\${this.baseUrl}/health\`);
                    if (response.ok) {
                        this.isConnected = true;
                        console.log('✅ Deno KV connected successfully');
                        return true;
                    }
                    throw new Error('KV health check failed');
                } catch (error) {
                    console.error('❌ Deno KV connection failed:', error);
                    this.isConnected = false;
                    return false;
                }
            }

            async set(key, value) {
                if (!this.isConnected) await this.connect();

                try {
                    const response = await fetch(\`\${this.baseUrl}/set\`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ key, value })
                    });
                    return await response.json();
                } catch (error) {
                    console.error('❌ Deno KV set error:', error);
                    return { success: false, error: error.message };
                }
            }

            async get(key) {
                if (!this.isConnected) await this.connect();

                try {
                    const response = await fetch(\`\${this.baseUrl}/get\`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ key })
                    });
                    return await response.json();
                } catch (error) {
                    console.error('❌ Deno KV get error:', error);
                    return { success: false, error: error.message };
                }
            }

            async list(prefix) {
                if (!this.isConnected) await this.connect();

                try {
                    const response = await fetch(\`\${this.baseUrl}/list\`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ prefix })
                    });
                    return await response.json();
                } catch (error) {
                    console.error('❌ Deno KV list error:', error);
                    return { success: false, error: error.message };
                }
            }
        }

        // 全域 Deno KV 實例
        window.denoKV = new DenoKVAdapter();

        // 🤖 AIAPIAdapter - 統一的 AI API 介面 (Deno 版本)
        class DenoAIAPIAdapter {
            constructor(provider = 'openrouter') {
                this.provider = provider;
                this.baseUrl = '/api/deno/chat/chat';
                this.isEnhanced = false;
                this.defaultModel = 'openai/gpt-4o-mini';
            }

            async callAI(message, options = {}) {
                try {
                    console.log('🤖 Deno AIAPIAdapter 調用:', this.provider, message.substring(0, 50) + '...');

                    const response = await fetch(this.baseUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            message: message,
                            provider: this.provider,
                            model: options.model || this.defaultModel,
                            conversationHistory: options.conversationHistory || [],
                            systemPrompt: options.systemPrompt || this.getDefaultSystemPrompt()
                        })
                    });

                    if (!response.ok) {
                        throw new Error(\`API 錯誤: \${response.status}\`);
                    }

                    const data = await response.json();

                    if (!data.success) {
                        throw new Error(data.error || '未知錯誤');
                    }

                    return {
                        success: true,
                        response: data.response,
                        provider: data.provider || this.provider,
                        model: data.model,
                        timestamp: data.timestamp
                    };

                } catch (error) {
                    console.error('❌ Deno AIAPIAdapter 錯誤:', error);
                    throw error;
                }
            }

            getDefaultSystemPrompt() {
                return \`你是 HSN (HeartSync Network) 的 AI 知識探索夥伴，現在運行在 Deno + Datastar 技術棧上。你的任務是：

1. * **知識探索**: 幫助用戶深入探索各種主題和概念，運用最新的推理能力
2. + **友善對話**: 保持溫暖、友善且富有洞察力的對話風格
3. = **簡潔回應**: 提供有價值但簡潔的回應，避免過長的解釋
4. | **啟發思考**: 鼓勵用戶進一步思考和探索
5. - **前沿技術**: 運用 2025 年最新的 Deno + Datastar 技術為用戶提供更好的體驗

請用繁體中文回應，保持專業但親切的語調。重要：完全避免使用現代表情符號，改用復古 ASCII 表情符號如 =__= (無奈) ^o^ (開心) XD (大笑) Orz (崩潰) Q_Q (哭泣) @_@ (暈眩) >_< (困擾) T_T (難過) 以及終端符號如 * + - = | 等。偶爾可以使用注音文如「ㄅ錯」「ㄏㄏ」「ㄎㄎ」增加親切感。\`;
            }
        }

        // 全域 AI API 實例 (Deno 版本)
        window.aiAPI = new DenoAIAPIAdapter();
    </script>

    <style>
        /* ========================================
         * 🎨 HSN CLI PREVIEW DENO - CSS 樣式索引
         * ========================================
         *
         * 📋 目錄索引 (Table of Contents)
         * ────────────────────────────────────────
         * 1. 🎯 CSS 變數定義 (CSS Variables)
         * 2. 🔧 基礎重置 (Base Reset)
         * 3. 🖥️ 終端容器 (Terminal Container)
         * 4. 📝 標題區域 (Header Section)
         * 5. 💬 聊天介面 (Chat Interface)
         * 6. 🎭 水母頭像系統 (Jellyfish Avatar)
         * 7. 🌈 動畫定義 (Animations)
         * 8. 📱 響應式設計 (Responsive)
         * 9. 🎨 主題色彩 (Theme Colors)
         * 10. 🔍 搜索優化 (Search Optimization)
         *
         * 🏷️ 搜索標籤: terminal, retro, amber, jellyfish, crt, neon, deno, datastar
         * ======================================== */

        /* ========================================
         * 1. 🎯 CSS 變數定義 (CSS Variables) - Deno 版本
         * ======================================== */
        :root {
            /* 🎨 主色系 - 增強磷光綠主題 (Enhanced Phosphor Green) */
            --color-primary: #00ff41;        /* 螢光綠 - 主要色彩 */
            --color-secondary: #39ff14;      /* 亮綠 - 次要色彩 */
            --color-accent: #00ffff;         /* 青色強調 */
            --color-background: #0a0a0a;     /* 深黑背景 - 護眼優化 */
            --color-error: #ff4757;          /* 錯誤紅 - 提升對比度 */

            /* 🌈 擴展色彩系統 - 2025 升級版 */
            --color-primary-amber: #ffa726;  /* 琥珀主色 - 提升亮度 */
            --color-secondary-amber: #ff8f00; /* 琥珀次色 */
            --color-tertiary-amber: #ffcc80; /* 琥珀三級 - 提升可讀性 */
            --color-muted-amber: #ff8a65;    /* 琥珀靜音 */
            --color-dark-amber: #e65100;     /* 琥珀深色 */

            /* 🌟 霓虹色彩 - 2025 增強版 */
            --color-neon-cyan: #00e5ff;     /* 青色霓虹 - 提升飽和度 */
            --color-neon-magenta: #e91e63;  /* 洋紅霓虹 - 護眼優化 */
            --color-neon-yellow: #ffeb3b;   /* 黃色霓虹 - 提升對比 */
            --color-neon-green: #00ff41;    /* 綠色霓虹 - 保持經典 */

            /* 🖤 背景色系 - 護眼深色優化 */
            --bg-primary: linear-gradient(135deg, #0a0a0a 0%, #121212 100%);
            --bg-secondary: linear-gradient(135deg, #000000 0%, #0a0a0a 100%);
            --bg-tertiary: rgba(0, 0, 0, 0.98); /* 提升不透明度 */

            /* 🔤 字體系統 - 2025 可讀性優化 */
            --font-mono: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
            --font-size-lg: 2rem;           /* 大標題 - 手機端優化 */
            --font-size-md: 1.3rem;         /* 中標題 - 提升可讀性 */
            --font-size-sm: 1.1rem;         /* 內文 - 手機端友好 */
            --font-size-xs: 0.9rem;         /* 小字 - 最小可讀尺寸 */

            /* 📏 間距系統 */
            --space-xs: 0.25rem;
            --space-sm: 0.5rem;
            --space-md: 1rem;
            --space-lg: 1.5rem;
            --space-xl: 2rem;

            /* 保持向後兼容 */
            --spacing-xs: 4px;
            --spacing-sm: 8px;
            --spacing-md: 12px;
            --spacing-lg: 20px;
            --spacing-xl: 30px;

            /* ⏱️ 動畫時間 */
            --anim-fast: 0.3s;
            --anim-normal: 1s;
            --anim-slow: 3s;
            --anim-very-slow: 8s;

            /* 🎭 復古效果 */
            --glow-intensity: 0 0 8px var(--color-primary);
            --scan-line-opacity: 0.03;
            --terminal-border: 1px solid rgba(0, 255, 65, 0.1);

            /* 🌟 陰影效果 */
            --shadow-glow-amber: 0 0 15px var(--color-primary-amber);
            --shadow-glow-strong: 0 0 25px rgba(255, 176, 0, 0.5), 0 0 35px rgba(255, 128, 0, 0.3);
            --shadow-glow-green: 0 0 15px var(--color-primary);
        }
        /* ========================================
         * 1.5 🎨 主題系統 (Theme System) - Deno 版本
         * ======================================== */

        /* 🟢 增強磷光綠主題 (預設) */
        [data-theme="retro"], [data-theme="enhanced-phosphor"] {
            --color-primary: #00ff41;
            --color-secondary: #39ff14;
            --color-accent: #00e5ff;
            --color-primary-amber: #00ff41;      /* 改為磷光綠 */
            --color-secondary-amber: #39ff14;    /* 改為亮綠 */
            --color-tertiary-amber: #80ff80;     /* 改為淺綠 */
            --color-muted-amber: #66ff66;        /* 改為中綠 */
            --color-neon-green: #00ff41;
            --glow-intensity: 0 0 12px var(--color-primary);

            /* 🎨 聊天區專用顏色 - 改為磷光綠系 */
            --chat-user-color: #00ff41;          /* 用戶訊息：磷光綠 */
            --chat-ai-color: #80ff80;            /* AI 訊息：淺綠 */
            --chat-system-color: #39ff14;        /* 系統訊息：亮綠 */
            --chat-border-color: #00ff41;        /* 邊框：磷光綠 */
        }

        /* 🌙 護眼深夜主題 - 低對比度護眼 */
        [data-theme="night-owl"] {
            --color-primary: #4fc3f7;        /* 柔和藍 */
            --color-secondary: #81c784;      /* 柔和綠 */
            --color-accent: #ffb74d;         /* 柔和橙 */
            --color-primary-amber: #ffb74d;  /* 暖橙 */
            --color-secondary-amber: #ff8a65; /* 柔和橙紅 */
            --color-tertiary-amber: #ffcc80; /* 淺橙 */
            --color-muted-amber: #a1887f;    /* 暖灰 */
            --color-neon-green: #81c784;
            --color-background: #0d1117;     /* GitHub 深色背景 */
            --glow-intensity: 0 0 6px var(--color-primary);

            /* 🎨 聊天區專用顏色 */
            --chat-user-color: #4fc3f7;
            --chat-ai-color: #81c784;
            --chat-system-color: #ffb74d;
            --chat-border-color: #4fc3f7;
        }

        /* 🚀 賽博朋克紫主題 - 現代復古融合 */
        [data-theme="cyberpunk-purple"] {
            --color-primary: #bb86fc;        /* 賽博紫 */
            --color-secondary: #03dac6;      /* 賽博青 */
            --color-accent: #cf6679;         /* 賽博粉 */
            --color-primary-amber: #ffa726;  /* 琥珀橙 */
            --color-secondary-amber: #ff7043; /* 深橙 */
            --color-tertiary-amber: #ffcc80; /* 淺橙 */
            --color-muted-amber: #9c27b0;    /* 深紫 */
            --color-neon-green: #03dac6;
            --color-background: #121212;     /* Material 深色 */
            --glow-intensity: 0 0 15px var(--color-primary);

            /* 🎨 聊天區專用顏色 */
            --chat-user-color: #bb86fc;
            --chat-ai-color: #03dac6;
            --chat-system-color: #cf6679;
            --chat-border-color: #bb86fc;
        }

        /* 💼 專業高對比主題 - 工作場景優化 */
        [data-theme="professional"] {
            --color-primary: #ffffff;        /* 純白 */
            --color-secondary: #e0e0e0;      /* 淺灰 */
            --color-accent: #2196f3;         /* 專業藍 */
            --color-primary-amber: #ff9800;  /* 專業橙 */
            --color-secondary-amber: #f57c00; /* 深橙 */
            --color-tertiary-amber: #ffcc80; /* 淺橙 */
            --color-muted-amber: #757575;    /* 中灰 */
            --color-neon-green: #4caf50;
            --color-background: #000000;     /* 純黑背景 */
            --glow-intensity: 0 0 8px var(--color-primary);

            /* 🎨 聊天區專用顏色 */
            --chat-user-color: #ffffff;
            --chat-ai-color: #e0e0e0;
            --chat-system-color: #2196f3;
            --chat-border-color: #ffffff;
        }

        /* ========================================
         * 2. 🔧 基礎重置 (Base Reset)
         * ======================================== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            animation-fill-mode: both;
            animation-timing-function: ease-out;
        }

        /* 🚀 硬體加速優化 */
        .terminal-container,
        .jellyfish-avatar,
        .chat-container,
        .mini-jellyfish-avatar {
            will-change: transform;
            transform: translateZ(0);
        }

        body {
            background: var(--bg-primary);
            color: var(--color-tertiary-amber);
            font-family: var(--font-mono);
            font-size: var(--font-size-base);
            line-height: 1.6;
            overflow-x: hidden;
            image-rendering: pixelated;
            text-rendering: optimizeSpeed;
            filter: contrast(1.1) brightness(0.9);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* ========================================
         * 3. 🖥️ 終端容器 (Terminal Container)
         * ======================================== */
        .terminal-container {
            padding: var(--spacing-lg);
            background: var(--bg-secondary);
            padding-bottom: 180px;
            border: var(--terminal-border);
            backdrop-filter: blur(10px);
            background: rgba(0, 0, 0, 0.2);
            box-shadow:
                inset 0 0 20px rgba(0, 255, 65, 0.05),
                0 0 30px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow-x: hidden;
        }

        /* ========================================
         * 4. 📝 標題區域 (Header Section)
         * ======================================== */
        .terminal-header {
            border-bottom: 2px solid var(--color-primary-amber);
            border-image: linear-gradient(90deg,
                transparent,
                var(--color-primary-amber),
                var(--color-secondary-amber),
                var(--color-primary-amber),
                transparent
            ) 1;
            padding-bottom: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
            position: relative;
        }

        .terminal-title {
            font-size: 1.8rem;
            font-weight: bold;
            text-align: center;
            margin-bottom: 10px;
            text-shadow: var(--shadow-glow-amber);
            color: var(--color-primary-amber);
            letter-spacing: 2px;
            image-rendering: pixelated;
            filter: contrast(1.2) brightness(0.8);
            position: relative;
        }

        .title-preview {
            font-size: 25%;
            opacity: 0.7;
            vertical-align: super;
        }

        .terminal-subtitle {
            text-align: center;
            opacity: 0.8;
            font-size: var(--font-size-sm);
            color: #d4a574;
            line-height: 1.4;
            max-width: 600px;
            margin: 0 auto;
            image-rendering: pixelated;
            filter: contrast(1.2) brightness(0.85);
        }

        .header-restore-hint {
            text-align: center;
            margin-bottom: 20px;
        }

        .restore-hint-text {
            font-size: var(--font-size-xs);
            color: var(--color-neon-green);
            opacity: 0.4;
            font-style: italic;
            text-shadow: 0 0 4px rgba(0, 255, 65, 0.3);
            filter: blur(0.5px);
        }

        /* ========================================
         * 5. 💬 聊天介面 (Chat Interface)
         * ======================================== */
        .chat-container {
            max-width: 800px;
            margin: 0 auto 20px auto;
            border-radius: 8px;
            padding: var(--spacing-lg);
            background: rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(8px);
            position: relative;
            max-height: 70vh;
            overflow: visible;
        }

        .chat-history {
            height: 60vh;
            max-height: 500px;
            overflow-y: auto;
            padding-right: 10px;
            padding-bottom: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(204, 153, 102, 0.1);
            border-radius: 8px;
        }

        .chat-message {
            margin-bottom: 15px;
        }

        .message-user {
            color: var(--chat-user-color);
            image-rendering: pixelated;
            filter: contrast(1.2) brightness(0.9);
        }

        .message-ai {
            color: var(--chat-ai-color);
            margin-left: var(--spacing-lg);
            border-left: 2px solid var(--chat-border-color);
            padding-left: 15px;
            image-rendering: pixelated;
            filter: contrast(1.2) brightness(0.9);
        }

        .message-system {
            color: var(--chat-system-color);
            margin-left: 10px;
            border-left: 2px solid var(--chat-system-color);
            padding-left: 10px;
            background: rgba(255, 170, 0, 0.1);
            border-radius: 4px;
        }

        /* 🎨 主題切換過渡效果 - 確保聊天區文字顏色會改變 */
        .message-user,
        .message-ai,
        .message-system,
        .chat-message {
            transition: color 0.3s ease, border-color 0.3s ease, background-color 0.3s ease;
        }

        /* ========================================
         * 6. 🎭 水母頭像系統 (Jellyfish Avatar)
         * ======================================== */
        .mini-jellyfish-avatar {
            display: inline-block;
            width: 16px;
            height: 16px;
            position: relative;
            margin-right: var(--spacing-xs);
            transform-style: flat;
            animation:
                gentle-float 8s ease-in-out infinite,
                slow-color-change 12s ease-in-out infinite,
                opacity-pulse 6s ease-in-out infinite;
            opacity: 0.7;
        }

        .mini-sphere {
            position: absolute;
            border-radius: 50%;
            background: linear-gradient(135deg,
                rgba(0, 255, 65, 0.6) 0%,
                rgba(0, 255, 65, 0.3) 100%);
            border: 1px solid rgba(0, 255, 65, 0.4);
            animation: gentle-float 8s ease-in-out infinite;
        }

        .mini-sphere-1 {
            width: 14px;
            height: 14px;
            top: 1px;
            left: 1px;
            animation-delay: 0s;
        }

        .mini-sphere-2 {
            width: 10px;
            height: 10px;
            top: 3px;
            left: 3px;
            opacity: 0.7;
            animation-delay: 2.7s;
        }

        .mini-sphere-3 {
            width: 6px;
            height: 6px;
            top: 5px;
            left: 5px;
            opacity: 0.5;
            animation-delay: 5.4s;
        }

        .mini-core {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 3px;
            height: 3px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.9);
            box-shadow: 0 0 4px rgba(0, 255, 65, 0.6);
            animation: gentle-core-pulse 4s ease-in-out infinite;
        }

        .ai-jellyfish-prefix {
            display: inline-block;
            animation: ai-jellyfish-glow 12s ease-in-out infinite;
            filter: hue-rotate(0deg);
        }

        .user-jellyfish-prefix {
            display: inline-block;
            animation: user-jellyfish-glow 10s ease-in-out infinite;
            filter: hue-rotate(0deg);
        }

        /* 🔷 用戶訊息專用 - 方形微型動畫 */
        .user-jellyfish-prefix .mini-sphere {
            border-radius: 2px; /* 方形而非圓形 */
            background: linear-gradient(135deg,
                var(--chat-user-color) 0%,
                rgba(255, 167, 38, 0.6) 100%);
            border: 1px solid var(--chat-user-color);
        }

        .user-jellyfish-prefix .mini-sphere-1 {
            width: 12px;
            height: 12px;
            top: 2px;
            left: 2px;
            transform: rotate(45deg);
            animation: user-diamond-spin 8s linear infinite;
        }

        .user-jellyfish-prefix .mini-sphere-2 {
            width: 8px;
            height: 8px;
            top: 4px;
            left: 4px;
            transform: rotate(45deg);
            opacity: 0.7;
            animation: user-diamond-spin 8s linear infinite reverse;
            animation-delay: 2s;
        }

        .user-jellyfish-prefix .mini-sphere-3 {
            width: 4px;
            height: 4px;
            top: 6px;
            left: 6px;
            transform: rotate(45deg);
            opacity: 0.5;
            animation: user-diamond-spin 8s linear infinite;
            animation-delay: 4s;
        }

        .user-jellyfish-prefix .mini-core {
            width: 2px;
            height: 2px;
            background: var(--chat-user-color);
            box-shadow: 0 0 4px var(--chat-user-color);
            animation: user-core-pulse 4s ease-in-out infinite;
        }

        /* ========================================
         * 7. 💻 輸入區域 (Input Area)
         * ======================================== */
        .prompt-line {
            display: flex;
            align-items: flex-start;
            position: fixed;
            bottom: 60px;
            left: 0;
            right: 0;
            margin: 0;
            padding: calc(var(--spacing-md) * 2) var(--spacing-lg);
            background: var(--bg-secondary);
            border-radius: 8px 8px 0 0;
            border-top: 1px solid rgba(204, 153, 102, 0.2);
            z-index: 100;
            min-height: 80px;
        }

        .prompt-symbol {
            color: var(--color-primary-amber);
            margin-right: 10px;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .input-container {
            display: flex;
            align-items: flex-start;
            gap: 8px;
            flex: 1;
            width: 100%;
            background: rgba(0, 0, 0, 0.3);
            border: var(--terminal-border);
            border-radius: 6px;
            padding: 8px;
            transition: background-color 0.2s ease, box-shadow 0.2s ease;
            position: relative;
        }

        .input-container:focus-within {
            background: rgba(0, 0, 0, 0.4);
            box-shadow: 0 0 12px rgba(0, 255, 65, 0.2);
        }

        .prompt-input {
            flex: 1;
            background: transparent;
            border: none;
            color: var(--color-primary-amber);
            font-family: inherit;
            font-size: inherit;
            outline: none;
            caret-color: rgba(0, 0, 0, 0.3);
            min-width: 0;
            width: 100%;
            min-height: 24px;
            max-height: 120px;
            line-height: 1.4;
            padding: 4px 0;
            overflow-y: auto;
            resize: none;
        }

        .prompt-input::placeholder {
            color: #666666;
            opacity: 0.8;
        }

        .mobile-send-button {
            background: transparent;
            border: 2px solid #00ff41;
            color: #00ff41;
            padding: 8px 16px;
            border-radius: 0;
            font-family: 'Courier New', monospace;
            font-weight: bold;
            font-size: 11px;
            letter-spacing: 1px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 70px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            white-space: nowrap;
            position: relative;
            z-index: 10;
            text-transform: uppercase;
            box-shadow:
                inset 0 0 0 1px rgba(0, 255, 65, 0.3),
                0 0 10px rgba(0, 255, 65, 0.2);
        }

        .mobile-send-button:hover:not(:disabled) {
            background: rgba(0, 255, 65, 0.1);
            border-color: #00ff65;
            color: #00ff65;
            box-shadow:
                inset 0 0 0 1px rgba(0, 255, 65, 0.5),
                0 0 15px rgba(0, 255, 65, 0.4),
                0 0 25px rgba(0, 255, 65, 0.2);
            text-shadow: 0 0 8px rgba(0, 255, 65, 0.6);
        }

        .mobile-send-button:disabled {
            opacity: 0.4;
            cursor: not-allowed;
            background: transparent;
            border-color: rgba(0, 255, 65, 0.3);
            color: rgba(0, 255, 65, 0.3);
            box-shadow: none;
            text-shadow: none;
        }

        .mini-cursor {
            display: inline-block;
            width: 4px;
            height: 12px;
            background: var(--color-neon-green);
            animation: retro-cursor-blink 1s ease-in-out infinite;
            margin-left: 4px;
            vertical-align: middle;
            box-shadow: 0 0 6px rgba(0, 255, 65, 0.6);
        }

        /* ========================================
         * 8. 📊 狀態欄 (Status Bar)
         * ======================================== */
        .status-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(135deg, #1a1a1a 0%, #2a1a0a 100%);
            border-top: 2px solid var(--color-primary-amber);
            border-image: linear-gradient(90deg,
                var(--color-secondary-amber),
                var(--color-primary-amber),
                var(--color-secondary-amber)
            ) 1;
            padding: 10px var(--spacing-lg);
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: calc(var(--font-size-sm) * 0.8);
            color: #d4a574;
            z-index: 1000;
            box-shadow: 0 -5px 20px rgba(255, 176, 0, 0.1);
            writing-mode: horizontal-tb;
            text-orientation: mixed;
            white-space: nowrap;
            overflow: hidden;
        }

        .status-left, .status-right {
            display: flex;
            gap: 10px;
        }

        /* ========================================
         * 9. 🌈 動畫定義 (Animations)
         * ======================================== */
        @keyframes gentle-float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.8;
            }
            25% {
                transform: translateY(-2px) rotate(1deg);
                opacity: 0.9;
            }
            50% {
                transform: translateY(-1px) rotate(0deg);
                opacity: 1;
            }
            75% {
                transform: translateY(-2px) rotate(-1deg);
                opacity: 0.9;
            }
        }

        @keyframes gentle-core-pulse {
            0%, 100% {
                transform: translate(-50%, -50%) scale(1);
                opacity: 0.9;
            }
            50% {
                transform: translate(-50%, -50%) scale(1.1);
                opacity: 1;
            }
        }

        @keyframes slow-color-change {
            0% { filter: hue-rotate(0deg) saturate(1); }
            25% { filter: hue-rotate(90deg) saturate(1.2); }
            50% { filter: hue-rotate(180deg) saturate(0.8); }
            75% { filter: hue-rotate(270deg) saturate(1.1); }
            100% { filter: hue-rotate(360deg) saturate(1); }
        }

        @keyframes opacity-pulse {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 0.9; }
        }

        @keyframes ai-jellyfish-glow {
            0% {
                filter: hue-rotate(0deg) brightness(1.2);
                text-shadow: 0 0 8px var(--color-neon-cyan);
                color: var(--color-neon-cyan);
            }
            25% {
                filter: hue-rotate(90deg) brightness(1.4);
                text-shadow: 0 0 10px var(--color-neon-magenta);
                color: var(--color-neon-magenta);
            }
            50% {
                filter: hue-rotate(180deg) brightness(1.2);
                text-shadow: 0 0 8px var(--color-neon-yellow);
                color: var(--color-neon-yellow);
            }
            75% {
                filter: hue-rotate(270deg) brightness(1.4);
                text-shadow: 0 0 10px rgba(0, 255, 128, 1);
                color: #00ff80;
            }
            100% {
                filter: hue-rotate(360deg) brightness(1.2);
                text-shadow: 0 0 8px var(--color-neon-cyan);
                color: var(--color-neon-cyan);
            }
        }

        @keyframes user-jellyfish-glow {
            0% {
                filter: hue-rotate(0deg) brightness(1.1);
                text-shadow: 0 0 6px rgba(255, 204, 51, 1);
                color: #ffcc33;
            }
            25% {
                filter: hue-rotate(30deg) brightness(1.3);
                text-shadow: 0 0 8px var(--color-primary-amber);
                color: var(--color-primary-amber);
            }
            50% {
                filter: hue-rotate(60deg) brightness(1.1);
                text-shadow: 0 0 6px var(--color-secondary-amber);
                color: var(--color-secondary-amber);
            }
            75% {
                filter: hue-rotate(30deg) brightness(1.3);
                text-shadow: 0 0 8px var(--color-tertiary-amber);
                color: var(--color-tertiary-amber);
            }
            100% {
                filter: hue-rotate(0deg) brightness(1.1);
                text-shadow: 0 0 6px rgba(255, 204, 51, 1);
                color: #ffcc33;
            }
        }

        @keyframes retro-cursor-blink {
            0%, 50% {
                opacity: 1;
                background: var(--color-neon-green);
                box-shadow: 0 0 8px rgba(0, 255, 65, 0.8);
            }
            51%, 100% {
                opacity: 0;
                background: transparent;
                box-shadow: none;
            }
        }

        /* 🎭 水母動畫關鍵幀 */
        @keyframes jellyfish-float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            25% {
                transform: translateY(-10px) rotate(2deg);
            }
            50% {
                transform: translateY(-5px) rotate(0deg);
            }
            75% {
                transform: translateY(-15px) rotate(-2deg);
            }
        }

        @keyframes jellyfish-pulse {
            0%, 100% {
                transform: scale(1);
                opacity: 0.8;
            }
            50% {
                transform: scale(1.1);
                opacity: 1;
            }
        }

        @keyframes jellyfish-core-glow {
            0%, 100% {
                box-shadow: 0 0 12px var(--color-accent);
                transform: translate(-50%, -50%) scale(1);
            }
            50% {
                box-shadow: 0 0 20px var(--color-accent);
                transform: translate(-50%, -50%) scale(1.2);
            }
        }

        @keyframes tentacle-sway {
            0%, 100% {
                transform: rotate(0deg);
            }
            25% {
                transform: rotate(5deg);
            }
            75% {
                transform: rotate(-5deg);
            }
        }

        /* 🔷 用戶方形動畫關鍵幀 */
        @keyframes user-diamond-spin {
            0% {
                transform: rotate(45deg) scale(1);
            }
            25% {
                transform: rotate(135deg) scale(1.1);
            }
            50% {
                transform: rotate(225deg) scale(1);
            }
            75% {
                transform: rotate(315deg) scale(0.9);
            }
            100% {
                transform: rotate(405deg) scale(1);
            }
        }

        @keyframes user-core-pulse {
            0%, 100% {
                transform: translate(-50%, -50%) scale(1);
                opacity: 0.8;
            }
            50% {
                transform: translate(-50%, -50%) scale(1.5);
                opacity: 1;
            }
        }

        /* ========================================
         * 10. 📱 響應式設計 (Responsive)
         * ======================================== */
        @media (max-width: 768px) {
            :root {
                --font-size-lg: 2.2rem;
                --font-size-md: 1.4rem;
                --font-size-sm: 1.2rem;
                --font-size-xs: 1rem;
            }

            .mobile-send-button {
                display: flex;
                padding: 14px 18px;
                min-width: 80px;
                height: 44px;
                font-size: 13px;
                align-self: flex-end;
                margin-bottom: 2px;
                border-width: 2px;
                box-shadow:
                    inset 0 0 0 1px rgba(0, 255, 65, 0.3),
                    0 0 10px rgba(0, 255, 65, 0.15);
            }

            .prompt-input {
                font-size: 16px;
                min-height: 32px;
                line-height: 1.4;
                padding: 8px 0;
            }

            .message-user, .message-ai {
                font-size: 1.1rem;
                line-height: 1.6;
                margin-bottom: 18px;
            }

            .terminal-title {
                font-size: 2rem;
                line-height: 1.3;
            }

            .terminal-subtitle {
                font-size: 1.1rem;
                line-height: 1.5;
            }

            .prompt-line {
                position: fixed;
                bottom: 100px;
                left: 0;
                right: 0;
                margin: 0;
                padding: var(--spacing-sm) var(--spacing-md);
                background: rgba(0, 0, 0, 0.85);
                backdrop-filter: blur(20px) saturate(180%);
                -webkit-backdrop-filter: blur(20px) saturate(180%);
                border-top: 1px solid rgba(0, 255, 65, 0.2);
                box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
                z-index: 200;
            }

            .chat-history {
                max-height: calc(100vh - 200px - 120px);
                padding-bottom: 140px;
                margin-bottom: 0;
            }

            .terminal-container {
                padding-bottom: 160px;
            }

            .status-bar {
                font-size: 11px;
                padding: 8px var(--spacing-md);
                height: 50px;
                writing-mode: horizontal-tb !important;
                text-orientation: mixed !important;
                white-space: nowrap !important;
                flex-wrap: nowrap;
                overflow: hidden;
            }
        }

        @media (min-width: 769px) {
            .mobile-send-button {
                display: none;
            }
        }

        /* ========================================
         * 11. 🎨 1980s 極簡主題切換圓點
         * ======================================== */
        .theme-switcher {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .theme-dot {
            width: 12px;
            height: 12px;
            border: 1px solid var(--color-primary);
            border-radius: 50%;
            background: transparent;
            transition: all 0.3s ease;
            position: relative;
        }

        .theme-dot::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: var(--color-primary);
            opacity: 0.3;
            transition: all 0.3s ease;
        }

        .theme-switcher:hover .theme-dot {
            transform: scale(1.2);
            box-shadow: 0 0 8px var(--color-primary);
        }

        .theme-switcher:hover .theme-dot::before {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1.2);
        }

        /* ========================================
         * 12. 🎭 水母標題動畫系統
         * ======================================== */
        .text-header {
            transition: opacity 2s ease-in-out;
        }

        .jellyfish-header {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            transition: opacity 2s ease-in-out;
        }

        .jellyfish-avatar {
            width: 80px;
            height: 80px;
            position: relative;
            margin: 0 auto;
            animation: jellyfish-float 6s ease-in-out infinite;
        }

        .jellyfish-sphere {
            position: absolute;
            border-radius: 50%;
            background: linear-gradient(135deg,
                var(--color-primary) 0%,
                var(--color-secondary) 50%,
                var(--color-accent) 100%);
            border: 1px solid var(--color-primary);
            animation: jellyfish-pulse 4s ease-in-out infinite;
        }

        .jellyfish-sphere-1 {
            width: 60px;
            height: 60px;
            top: 10px;
            left: 10px;
            animation-delay: 0s;
        }

        .jellyfish-sphere-2 {
            width: 40px;
            height: 40px;
            top: 20px;
            left: 20px;
            opacity: 0.8;
            animation-delay: 1.3s;
        }

        .jellyfish-sphere-3 {
            width: 20px;
            height: 20px;
            top: 30px;
            left: 30px;
            opacity: 0.6;
            animation-delay: 2.6s;
        }

        .jellyfish-core {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--color-accent);
            box-shadow: 0 0 12px var(--color-accent);
            animation: jellyfish-core-glow 3s ease-in-out infinite;
        }

        .jellyfish-tentacles {
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
        }

        .tentacle {
            position: absolute;
            width: 2px;
            height: 30px;
            background: linear-gradient(to bottom,
                var(--color-primary) 0%,
                transparent 100%);
            border-radius: 1px;
            transform-origin: top center;
        }

        .tentacle-1 {
            left: 30%;
            animation: tentacle-sway 3s ease-in-out infinite;
            animation-delay: 0s;
        }

        .tentacle-2 {
            left: 45%;
            animation: tentacle-sway 3s ease-in-out infinite;
            animation-delay: 0.5s;
        }

        .tentacle-3 {
            left: 55%;
            animation: tentacle-sway 3s ease-in-out infinite;
            animation-delay: 1s;
        }

        .tentacle-4 {
            left: 70%;
            animation: tentacle-sway 3s ease-in-out infinite;
            animation-delay: 1.5s;
        }
    </style>
</head>
<body>

    <!-- 🖥️ 終端容器 -->
    <div class="terminal-container">
        <!-- 📝 標題區域 -->
        <div class="terminal-header">
            <!-- 🎭 文字標題 (會淡出) -->
            <div class="text-header" id="textHeader">
                <h1 class="terminal-title">
                    ▲ ■ ◆ ▼ ◆ ■ ▲ <span class="title-preview">DENO</span>
                </h1>
                <p class="terminal-subtitle" style="font-size: 50%;">
                    人工智慧時代的新社交媒體 | Powered by Deno + Datastar
                </p>

                <!-- 🌅 標題恢復提示 -->
                <div class="header-restore-hint">
                    <p class="restore-hint-text">
                        點擊右上角圓點切換主題
                    </p>
                </div>
            </div>

            <!-- 🎭 水母動畫 (會淡入) -->
            <div class="jellyfish-header" id="jellyfishHeader" style="opacity: 0;">
                <div class="jellyfish-avatar">
                    <div class="jellyfish-sphere jellyfish-sphere-1"></div>
                    <div class="jellyfish-sphere jellyfish-sphere-2"></div>
                    <div class="jellyfish-sphere jellyfish-sphere-3"></div>
                    <div class="jellyfish-core"></div>
                    <div class="jellyfish-tentacles">
                        <div class="tentacle tentacle-1"></div>
                        <div class="tentacle tentacle-2"></div>
                        <div class="tentacle tentacle-3"></div>
                        <div class="tentacle tentacle-4"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 💬 聊天容器 -->
        <div class="chat-container">
            <!-- 聊天歷史 -->
            <div class="chat-history" id="chatHistory">
                <!-- 歡迎訊息 -->
                <div class="chat-message">
                    <div class="message-system">
                        <span style="opacity: 0.6;" id="welcomeTimestamp">
                            <span></span>
                        </span>
                        Deno > 歡迎使用 HSN CLI Deno 版本！這是基於 Deno + Datastar + Deno KV 的現代化聊天界面。
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 💻 輸入提示符 - 固定底部 -->
    <div class="prompt-line">
        <div class="prompt-symbol">
            <div class="mini-jellyfish-avatar user-jellyfish-prefix">
                <div class="mini-sphere mini-sphere-1"></div>
                <div class="mini-sphere mini-sphere-2"></div>
                <div class="mini-sphere mini-sphere-3"></div>
                <div class="mini-core"></div>
            </div>
            <div class="mini-cursor"></div>
        </div>

        <div class="input-container">
            <textarea
                class="prompt-input"
                placeholder="輸入您的訊息... (Enter 發送, Shift+Enter 換行)"
                id="messageInput"
                rows="1"
                style="resize: none; overflow: hidden;">
            </textarea>

            <!-- 📱 移動端發送按鈕 -->
            <button
                class="mobile-send-button"
                id="sendButton">
                <span id="sendButtonText">發送</span>
            </button>
        </div>
    </div>

    <!-- 📊 狀態欄 -->
    <div class="status-bar">
        <div class="status-left">
            <span id="kvStatus">Deno KV: 未連接</span>
            <span id="aiStatus">| AI: ready</span>
        </div>
        <div class="status-right">
            <span>Deno 技術棧 | Datastar 響應式 | Deno KV 存儲</span>
            <span id="themeStatus">| 主題: enhanced-phosphor</span>
            <span id="messageCount">| 訊息: 0</span>
        </div>
    </div>

    <!-- 🎨 1980s 極簡主題切換圓點 -->
    <div class="theme-switcher" id="themeSwitcher" title="點擊切換主題">
        <div class="theme-dot"></div>
    </div>

    <script>
        // 🦕 Datastar 應用初始化
        function initializeApp() {
            console.log('🚀 初始化 HSN CLI Preview Deno 版本...');

            // 初始化 Deno KV 連接
            initializeDenoKV();

            // 設置初始主題
            switchTheme('enhanced-phosphor');

            // 載入聊天歷史
            loadChatHistory();

            console.log('✅ 應用初始化完成');
        }

        // 🔌 初始化 Deno KV 連接
        async function initializeDenoKV() {
            try {
                const connected = await window.denoKV.connect();
                updateStore({ isConnected: connected });

                if (connected) {
                    console.log('✅ Deno KV 連接成功');
                } else {
                    console.warn('⚠️ Deno KV 連接失敗，使用本地存儲');
                }
            } catch (error) {
                console.error('❌ Deno KV 初始化錯誤:', error);
                updateStore({ isConnected: false });
            }
        }

        // 💾 載入聊天歷史
        async function loadChatHistory() {
            try {
                const result = await window.denoKV.get(['chat', 'history']);
                if (result.success && result.value) {
                    updateStore({ chatHistory: result.value });
                    renderChatHistory();
                }
            } catch (error) {
                console.error('❌ 載入聊天歷史失敗:', error);
                // 從 localStorage 載入備份
                const backup = localStorage.getItem('hsn-chat-history-deno');
                if (backup) {
                    const history = JSON.parse(backup);
                    updateStore({ chatHistory: history });
                    renderChatHistory();
                }
            }
        }

        // 💾 保存聊天歷史
        async function saveChatHistory(history) {
            try {
                // 保存到 Deno KV
                await window.denoKV.set(['chat', 'history'], history);

                // 備份到 localStorage
                localStorage.setItem('hsn-chat-history-deno', JSON.stringify(history));
            } catch (error) {
                console.error('❌ 保存聊天歷史失敗:', error);
                // 至少保存到 localStorage
                localStorage.setItem('hsn-chat-history-deno', JSON.stringify(history));
            }
        }

        // 🎨 主題切換
        function switchTheme(theme, showChatMessage = false) {
            document.documentElement.setAttribute('data-theme', theme);
            updateStore({ currentTheme: theme });

            // 保存主題偏好
            localStorage.setItem('hsn-theme-deno', theme);

            console.log('🎨 主題已切換至:', theme);

            // 如果需要顯示聊天訊息
            if (showChatMessage) {
                const themeNames = {
                    'enhanced-phosphor': '增強磷光綠',
                    'night-owl': '護眼深夜',
                    'cyberpunk-purple': '賽博朋克紫',
                    'professional': '專業高對比'
                };

                const themeName = themeNames[theme] || theme;
                const timestamp = new Date().toLocaleTimeString();

                // 添加主題切換訊息到聊天記錄
                const chatHistory = document.getElementById('chatHistory');
                if (chatHistory) {
                    const messageDiv = document.createElement('div');
                    messageDiv.className = 'chat-message';
                    messageDiv.innerHTML = \`
                        <div class="message-system">
                            <div class="message-content">
                                <span class="timestamp">\${timestamp}</span>
                                [sys] > [cfg] > [*] 主題已切換至『\${themeName}』 ^o^
                            </div>
                        </div>
                    \`;
                    chatHistory.appendChild(messageDiv);
                    chatHistory.scrollTop = chatHistory.scrollHeight;
                }
            }
        }

        // 🔄 循環主題切換 (同心圓點使用)
        function cycleTheme() {
            const themes = ['enhanced-phosphor', 'night-owl', 'cyberpunk-purple', 'professional'];
            const themeNames = {
                'enhanced-phosphor': '增強磷光綠',
                'night-owl': '護眼深夜',
                'cyberpunk-purple': '賽博朋克紫',
                'professional': '專業高對比'
            };

            const store = getStore();
            const currentIndex = themes.indexOf(store.currentTheme);
            const nextIndex = (currentIndex + 1) % themes.length;
            const nextTheme = themes[nextIndex];

            switchTheme(nextTheme, true); // 顯示聊天訊息

            // 更新圓點提示
            const themeSwitcher = document.getElementById('themeSwitcher');
            if (themeSwitcher) {
                themeSwitcher.title = '當前主題: ' + themeNames[nextTheme] + ' (點擊切換)';
            }
        }

        // ⌨️ 鍵盤事件處理
        function handleKeyDown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        // 📏 自動調整輸入框高度
        function autoResize(event) {
            const textarea = event.target;
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
        }

        // 📤 發送訊息
        async function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput?.value?.trim();

            if (!message) {
                console.warn('⚠️ 訊息為空，無法發送');
                return;
            }

            const store = getStore();
            if (store.isLoading) {
                console.warn('⚠️ 正在處理中，請稍候');
                return;
            }

            console.log('📤 發送訊息:', message);

            // 更新狀態
            updateStore({
                isLoading: true,
                aiStatus: 'thinking'
            });

            // 更新按鈕狀態
            const sendButton = document.getElementById('sendButton');
            const sendButtonText = document.getElementById('sendButtonText');
            if (sendButton && sendButtonText) {
                sendButton.disabled = true;
                sendButtonText.textContent = '發送中...';
            }

            // 清空輸入框
            if (messageInput) {
                messageInput.value = '';
                messageInput.style.height = 'auto';
            }

            // 添加用戶訊息
            const userMessage = {
                id: generateId(),
                type: 'user',
                content: message,
                timestamp: Date.now()
            };

            const newHistory = [...store.chatHistory, userMessage];
            updateStore({ chatHistory: newHistory });
            renderChatHistory();

            try {
                // 調用 AI API
                const response = await window.aiAPI.callAI(message, {
                    conversationHistory: newHistory.slice(-10) // 只傳送最近 10 條訊息
                });

                if (response.success) {
                    // 添加 AI 回應
                    const aiMessage = {
                        id: generateId(),
                        type: 'ai',
                        content: response.response,
                        timestamp: Date.now(),
                        model: response.model,
                        provider: response.provider
                    };

                    const finalHistory = [...newHistory, aiMessage];
                    updateStore({ chatHistory: finalHistory });
                    renderChatHistory();

                    // 保存聊天歷史
                    await saveChatHistory(finalHistory);
                } else {
                    throw new Error(response.error || '未知錯誤');
                }

            } catch (error) {
                console.error('❌ 發送訊息失敗:', error);

                // 添加錯誤訊息
                const errorMessage = {
                    id: generateId(),
                    type: 'system',
                    content: \`錯誤: \${error.message}\`,
                    timestamp: Date.now()
                };

                const errorHistory = [...newHistory, errorMessage];
                updateStore({ chatHistory: errorHistory });
                renderChatHistory();
            } finally {
                // 重置狀態
                updateStore({
                    isLoading: false,
                    aiStatus: 'ready'
                });

                // 重置按鈕狀態
                const sendButton = document.getElementById('sendButton');
                const sendButtonText = document.getElementById('sendButtonText');
                if (sendButton && sendButtonText) {
                    sendButton.disabled = false;
                    sendButtonText.textContent = '發送';
                }

                console.log('✅ 訊息處理完成，狀態已重置');
            }
        }

        // 🎨 渲染聊天歷史
        function renderChatHistory() {
            const store = getStore();
            const chatHistory = document.getElementById('chatHistory');
            if (!chatHistory) return;

            chatHistory.innerHTML = store.chatHistory.map(message => {
                const timestamp = new Date(message.timestamp).toLocaleTimeString();
                let prefix, cssClass;

                switch (message.type) {
                    case 'user':
                        prefix = '<div class="mini-jellyfish-avatar user-jellyfish-prefix"><div class="mini-sphere mini-sphere-1"></div><div class="mini-sphere mini-sphere-2"></div><div class="mini-sphere mini-sphere-3"></div><div class="mini-core"></div></div> >';
                        cssClass = 'message-user';
                        break;
                    case 'ai':
                        prefix = '<div class="mini-jellyfish-avatar ai-jellyfish-prefix"><div class="mini-sphere mini-sphere-1"></div><div class="mini-sphere mini-sphere-2"></div><div class="mini-sphere mini-sphere-3"></div><div class="mini-core"></div></div> >';
                        cssClass = 'message-ai';
                        break;
                    case 'system':
                        prefix = 'Deno >';
                        cssClass = 'message-system';
                        break;
                    default:
                        prefix = '❓ >';
                        cssClass = 'message-ai';
                }

                return \`
                    <div class="chat-message">
                        <div class="\${cssClass}">
                            <span style="opacity: 0.6;">\${timestamp}</span> \${prefix} \${message.content}
                        </div>
                    </div>
                \`;
            }).join('');

            // 滾動到底部
            setTimeout(() => {
                chatHistory.scrollTop = chatHistory.scrollHeight;
            }, 100);
        }

        // 🔧 工具函數
        function generateId() {
            return 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        function getStore() {
            // Datastar store 獲取邏輯
            return window.store || {
                currentTheme: 'enhanced-phosphor',
                chatHistory: [],
                currentMessage: '',
                isLoading: false,
                isConnected: false,
                aiStatus: 'ready',
                typingState: 'idle'
            };
        }

        function updateStore(updates) {
            // Datastar store 更新邏輯
            if (window.store) {
                Object.assign(window.store, updates);
            } else {
                window.store = { ...getStore(), ...updates };
            }

            // 更新狀態欄顯示
            updateStatusBar();
        }

        // 🔄 更新狀態欄顯示
        function updateStatusBar() {
            const store = getStore();

            // 更新 KV 連接狀態
            const kvStatus = document.getElementById('kvStatus');
            if (kvStatus) {
                kvStatus.textContent = 'Deno KV: ' + (store.isConnected ? '已連接' : '未連接');
            }

            // 更新 AI 狀態
            const aiStatus = document.getElementById('aiStatus');
            if (aiStatus) {
                aiStatus.textContent = '| AI: ' + store.aiStatus;
            }

            // 更新主題狀態
            const themeStatus = document.getElementById('themeStatus');
            if (themeStatus) {
                themeStatus.textContent = '主題: ' + store.currentTheme;
            }

            // 更新訊息計數
            const messageCount = document.getElementById('messageCount');
            if (messageCount) {
                messageCount.textContent = '| 訊息: ' + store.chatHistory.length;
            }

            // 更新歡迎訊息時間戳
            const welcomeTimestamp = document.getElementById('welcomeTimestamp');
            if (welcomeTimestamp && welcomeTimestamp.children[0]) {
                welcomeTimestamp.children[0].textContent = new Date().toLocaleTimeString();
            }
        }

        // 🚀 頁面載入完成後初始化
        document.addEventListener('DOMContentLoaded', () => {
            initializeApp();
            setupEventListeners();
            setupHeaderTransition();
        });

        // 🎭 設置標題轉換動畫
        function setupHeaderTransition() {
            // 8 秒後開始淡出文字標題，淡入水母動畫
            setTimeout(() => {
                const textHeader = document.getElementById('textHeader');
                const jellyfishHeader = document.getElementById('jellyfishHeader');

                if (textHeader && jellyfishHeader) {
                    console.log('🎭 開始標題轉換動畫...');

                    // 淡出文字標題
                    textHeader.style.opacity = '0';

                    // 淡入水母動畫
                    setTimeout(() => {
                        jellyfishHeader.style.opacity = '1';
                        console.log('✅ 水母動畫已淡入');
                    }, 1000); // 等待文字淡出一半時間後開始淡入水母
                }
            }, 8000); // 8 秒後開始轉換
        }

        // 🔧 設置事件監聽器
        function setupEventListeners() {
            // 輸入框事件
            const messageInput = document.getElementById('messageInput');
            if (messageInput) {
                messageInput.addEventListener('keydown', (event) => {
                    if (event.key === 'Enter' && !event.shiftKey) {
                        event.preventDefault();
                        sendMessage();
                    }
                });

                messageInput.addEventListener('input', (event) => {
                    autoResize(event);
                    updateStore({ currentMessage: event.target.value });
                });
            }

            // 發送按鈕事件
            const sendButton = document.getElementById('sendButton');
            if (sendButton) {
                sendButton.addEventListener('click', sendMessage);
            }

            // 主題切換事件 - 同心圓點
            const themeSwitcher = document.getElementById('themeSwitcher');
            if (themeSwitcher) {
                themeSwitcher.addEventListener('click', cycleTheme);
            }

            console.log('✅ 事件監聽器設置完成');
        }
    </script>
</body>
</html>`);
});

export default app;
