import { Hono } from 'hono';

const app = new Hono();

// 🦕 Deno KV API 適配器 - 為 CLI Preview Deno 版本提供 KV 存儲服務
// 這個 API 提供了與 Deno KV 兼容的接口，但在 Node.js 環境中運行
// 使用 localStorage 模擬 Deno KV 的功能

// 🔧 健康檢查端點
app.get('/health', (c) => {
    return c.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'Deno KV API Adapter',
        version: '1.0.0',
        environment: 'Node.js (Deno KV Compatible)',
        note: '這是一個 Deno KV 兼容的 API，在 Node.js 環境中運行'
    });
});

// 💾 設置鍵值對
app.post('/set', async (c) => {
    try {
        const { key, value } = await c.req.json();
        
        if (!key) {
            return c.json({
                success: false,
                error: 'Key is required'
            }, 400);
        }

        // 模擬 Deno KV 的鍵格式 (數組)
        const keyString = Array.isArray(key) ? key.join(':') : String(key);
        
        // 在實際的 Deno 環境中，這裡會使用 Deno.kv
        // 目前使用內存存儲模擬
        if (!global.denoKVStore) {
            global.denoKVStore = new Map();
        }
        
        const serializedValue = JSON.stringify({
            value: value,
            timestamp: Date.now(),
            versionstamp: generateVersionstamp()
        });
        
        global.denoKVStore.set(keyString, serializedValue);
        
        console.log(`🔧 Deno KV Set: ${keyString} = ${JSON.stringify(value).substring(0, 100)}...`);
        
        return c.json({
            success: true,
            key: keyString,
            versionstamp: generateVersionstamp()
        });
        
    } catch (error) {
        console.error('❌ Deno KV Set 錯誤:', error);
        return c.json({
            success: false,
            error: error.message
        }, 500);
    }
});

// 📖 獲取鍵值
app.post('/get', async (c) => {
    try {
        const { key } = await c.req.json();
        
        if (!key) {
            return c.json({
                success: false,
                error: 'Key is required'
            }, 400);
        }

        const keyString = Array.isArray(key) ? key.join(':') : String(key);
        
        if (!global.denoKVStore) {
            global.denoKVStore = new Map();
        }
        
        const storedData = global.denoKVStore.get(keyString);
        
        if (!storedData) {
            return c.json({
                success: true,
                value: null,
                versionstamp: null
            });
        }
        
        const parsed = JSON.parse(storedData);
        
        console.log(`🔍 Deno KV Get: ${keyString} = ${JSON.stringify(parsed.value).substring(0, 100)}...`);
        
        return c.json({
            success: true,
            value: parsed.value,
            versionstamp: parsed.versionstamp
        });
        
    } catch (error) {
        console.error('❌ Deno KV Get 錯誤:', error);
        return c.json({
            success: false,
            error: error.message
        }, 500);
    }
});

// 📋 列出鍵值對
app.post('/list', async (c) => {
    try {
        const { prefix } = await c.req.json();
        
        if (!global.denoKVStore) {
            global.denoKVStore = new Map();
        }
        
        const prefixString = Array.isArray(prefix) ? prefix.join(':') : String(prefix || '');
        const results = [];
        
        for (const [key, value] of global.denoKVStore.entries()) {
            if (!prefixString || key.startsWith(prefixString)) {
                try {
                    const parsed = JSON.parse(value);
                    results.push({
                        key: key.split(':'),
                        value: parsed.value,
                        versionstamp: parsed.versionstamp
                    });
                } catch (e) {
                    console.warn('⚠️ 解析存儲數據失敗:', key, e);
                }
            }
        }
        
        console.log(`📋 Deno KV List: prefix="${prefixString}", found ${results.length} items`);
        
        return c.json({
            success: true,
            entries: results,
            count: results.length
        });
        
    } catch (error) {
        console.error('❌ Deno KV List 錯誤:', error);
        return c.json({
            success: false,
            error: error.message
        }, 500);
    }
});

// 🗑️ 刪除鍵值對
app.post('/delete', async (c) => {
    try {
        const { key } = await c.req.json();
        
        if (!key) {
            return c.json({
                success: false,
                error: 'Key is required'
            }, 400);
        }

        const keyString = Array.isArray(key) ? key.join(':') : String(key);
        
        if (!global.denoKVStore) {
            global.denoKVStore = new Map();
        }
        
        const existed = global.denoKVStore.has(keyString);
        global.denoKVStore.delete(keyString);
        
        console.log(`🗑️ Deno KV Delete: ${keyString} (existed: ${existed})`);
        
        return c.json({
            success: true,
            deleted: existed
        });
        
    } catch (error) {
        console.error('❌ Deno KV Delete 錯誤:', error);
        return c.json({
            success: false,
            error: error.message
        }, 500);
    }
});

// 📊 統計信息
app.get('/stats', (c) => {
    try {
        if (!global.denoKVStore) {
            global.denoKVStore = new Map();
        }
        
        const totalKeys = global.denoKVStore.size;
        const keys = Array.from(global.denoKVStore.keys());
        
        // 按前綴分組統計
        const prefixStats = {};
        keys.forEach(key => {
            const prefix = key.split(':')[0];
            prefixStats[prefix] = (prefixStats[prefix] || 0) + 1;
        });
        
        return c.json({
            success: true,
            stats: {
                total_keys: totalKeys,
                prefix_breakdown: prefixStats,
                sample_keys: keys.slice(0, 10),
                memory_usage: 'N/A (in-memory store)'
            }
        });
        
    } catch (error) {
        console.error('❌ Deno KV Stats 錯誤:', error);
        return c.json({
            success: false,
            error: error.message
        }, 500);
    }
});

// 🧹 清空所有數據 (開發用)
app.post('/clear', (c) => {
    try {
        if (!global.denoKVStore) {
            global.denoKVStore = new Map();
        }
        
        const clearedCount = global.denoKVStore.size;
        global.denoKVStore.clear();
        
        console.log(`🧹 Deno KV Clear: 清空了 ${clearedCount} 個鍵值對`);
        
        return c.json({
            success: true,
            cleared_count: clearedCount
        });
        
    } catch (error) {
        console.error('❌ Deno KV Clear 錯誤:', error);
        return c.json({
            success: false,
            error: error.message
        }, 500);
    }
});

// 🔧 工具函數：生成版本戳
function generateVersionstamp() {
    // 模擬 Deno KV 的 versionstamp
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000000);
    return `${timestamp.toString(16)}-${random.toString(16)}`;
}

export default app;
