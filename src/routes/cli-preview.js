import { Hono } from 'hono';

const app = new Hono();

// 🔄 備份提醒: 在進行下一個任務前，請先備份此文件
// 當前版本: RETRO UI v2.0 全面升級完成版本 (2025-09-03)
// 上一版本: HSN-40 CSS 索引優化完成版本 (2025-06-27)
//
// 🚀 RETRO UI v2.0 升級內容:
// - Alpine.js 升級到 3.14.7
// - InstantDB 升級到 0.21.5
// - API 更換為 OpenRouter + Gemini 2.5 Pro
// - 新增 4 套主題: 增強磷光綠、護眼深夜、賽博朋克紫、專業高對比
// - 手機端可讀性大幅優化 (字體、對比度、觸控區域)
// - 修復測試目錄按鈕 404 問題
// - 新增主題切換功能與動畫效果

// 🖥️ HSN CLI Preview - 終端式 AI 社交體驗
app.get('/cli-preview', (c) => {
	return c.html(`<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HSN CLI Preview | 人工智慧時代的新社交媒體</title>

    <!-- Alpine.js 框架 - 升級到最新穩定版 -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.14.7/dist/cdn.min.js"></script>

    <!-- InstantDB 動態載入 -->
    <script>
        window.loadInstantDB = async () => {
            try {
                console.log('🔄 Loading InstantDB SDK...');
                const InstantDB = await import('https://cdn.skypack.dev/@instantdb/core@0.21.5');
                console.log('✅ InstantDB loaded successfully');

                if (!InstantDB.id) {
                    console.warn('⚠️ id 函數未找到，使用自定義實現');
                    InstantDB.id = () => {
                        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                            const r = Math.random() * 16 | 0;
                            const v = c == 'x' ? r : (r & 0x3 | 0x8);
                            return v.toString(16);
                        });
                    };
                }

                return InstantDB;
            } catch (error) {
                console.error('❌ InstantDB loading failed:', error);
                return null;
            }
        };

        // 🚀 HSN-47 Enhanced AI Integration
        // 全域變數
        let hsnAI = null;
        let isHSNAIEnabled = false;

        // 🤖 AIAPIAdapter - 統一的 AI API 介面 (OpenRouter 升級版)
        class AIAPIAdapter {
            constructor(provider = 'openrouter') {
                this.provider = provider;
                this.baseUrl = '/api/hsn/chat';
                this.isEnhanced = false;
                this.defaultModel = 'openai/gpt-4o-mini'; // OpenRouter 穩定且經濟的模型
            }

            async callAI(message, options = {}) {
                try {
                    console.log('🤖 AIAPIAdapter 調用:', this.provider, message.substring(0, 50) + '...');

                    const response = await fetch(this.baseUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            message: message,
                            provider: this.provider,
                            model: options.model || this.defaultModel,
                            conversationHistory: options.conversationHistory || [],
                            systemPrompt: options.systemPrompt || this.getDefaultSystemPrompt()
                        })
                    });

                    if (!response.ok) {
                        throw new Error(\`API 錯誤: \${response.status}\`);
                    }

                    const data = await response.json();

                    if (!data.success) {
                        throw new Error(data.error || '未知錯誤');
                    }

                    return {
                        success: true,
                        response: data.response,
                        provider: data.provider || this.provider,
                        model: data.model,
                        timestamp: data.timestamp
                    };

                } catch (error) {
                    console.error('❌ AIAPIAdapter 錯誤:', error);
                    throw error;
                }
            }

            getDefaultSystemPrompt() {
                return \`你是 HSN (HeartSync Network) 的 AI 知識探索夥伴，現在使用最新的 Gemini 2.5 Pro 模型。你的任務是：

1. * **知識探索**: 幫助用戶深入探索各種主題和概念，運用最新的推理能力
2. + **友善對話**: 保持溫暖、友善且富有洞察力的對話風格
3. = **簡潔回應**: 提供有價值但簡潔的回應，避免過長的解釋
4. | **啟發思考**: 鼓勵用戶進一步思考和探索
5. - **前沿技術**: 運用 2025 年最新的 AI 技術為用戶提供更好的體驗

請用繁體中文回應，保持專業但親切的語調。重要：完全避免使用現代表情符號，改用復古 ASCII 表情符號如 =__= (無奈) ^o^ (開心) XD (大笑) Orz (崩潰) Q_Q (哭泣) @_@ (暈眩) >_< (困擾) T_T (難過) 以及終端符號如 * + - = | 等。偶爾可以使用注音文如「ㄅ錯」「ㄏㄏ」「ㄎㄎ」增加親切感。\`;
            }
        }

        // 🚀 Enhanced AI API Adapter (HSN-47) - OpenRouter 升級版
        class EnhancedAIAPIAdapter {
            constructor() {
                this.hsnAI = null;
                this.fallbackAPI = new AIAPIAdapter('openrouter');
                this.isInitialized = false;
                this.isEnhanced = true;
                this.preferredModels = [
                    'openai/gpt-4o-mini',
                    'google/gemini-2.5-pro',
                    'meta-llama/llama-3.1-405b-instruct'
                ];
            }

            async initialize(container) {
                try {
                    // 動態載入 HSN AI Integration (模擬)
                    // 實際實作中會從模組載入
                    console.log('🚀 正在初始化 Enhanced AI Integration...');

                    // 模擬 HSN AI 初始化
                    this.hsnAI = {
                        initialized: true,
                        sendMessage: async (message, options) => {
                            // 這裡會調用實際的 HSN AI Integration
                            console.log('🌊 使用 Enhanced AI (含流式回應)');
                            const result = await this.fallbackAPI.callAI(message, options);
                            // 確保返回正確的模型資訊
                            if (result.success) {
                                result.modelTier = 2; // 預設平衡層級
                                result.responseTime = Date.now() - (result.startTime || Date.now());
                                result.streamingUsed = true;
                            }
                            return result;
                        },
                        setStreamingMode: (mode) => {
                            console.log('🌊 流式模式切換:', mode);
                        },
                        getStatusReport: () => ({
                            openrouter: {
                                currentModel: {
                                    name: 'GPT-4o Mini',
                                    apiName: 'openai/gpt-4o-mini',
                                    level: 1,
                                    provider: 'OpenAI',
                                    year: '2024'
                                }
                            },
                            streaming: { isActive: false, mode: 'Natural' },
                            performance: { health: 98 }
                        })
                    };

                    this.isInitialized = true;
                    console.log('✅ Enhanced AI Integration 初始化成功');

                } catch (error) {
                    console.error('❌ Enhanced AI Integration 初始化失敗:', error);
                    this.isInitialized = false;
                }
            }

            async callAI(message, options = {}) {
                if (this.isInitialized && this.hsnAI) {
                    try {
                        return await this.hsnAI.sendMessage(message, options);
                    } catch (error) {
                        console.warn('⚠️ Enhanced AI 失敗，降級到標準模式:', error);
                    }
                }

                // 降級到標準 API
                return await this.fallbackAPI.callAI(message, options);
            }

            getDefaultSystemPrompt() {
                return this.fallbackAPI.getDefaultSystemPrompt();
            }

            getStatusReport() {
                if (this.isInitialized && this.hsnAI) {
                    return this.hsnAI.getStatusReport();
                }
                return { mode: 'fallback', enhanced: false };
            }

            setStreamingMode(mode) {
                if (this.isInitialized && this.hsnAI) {
                    this.hsnAI.setStreamingMode(mode);
                }
            }
        }

        // 全域 AI API 實例 (Enhanced)
        window.aiAPI = new EnhancedAIAPIAdapter();
    </script>

    <style>
        /* ========================================
         * 🎨 HSN CLI PREVIEW - CSS 樣式索引
         * ========================================
         *
         * 📋 目錄索引 (Table of Contents)
         * ────────────────────────────────────────
         * 1. 🎯 CSS 變數定義 (CSS Variables)
         * 2. 🔧 基礎重置 (Base Reset)
         * 3. 🖥️ 終端容器 (Terminal Container)
         * 4. 📝 標題區域 (Header Section)
         * 5. 💬 聊天介面 (Chat Interface)
         * 6. 🎭 水母頭像系統 (Jellyfish Avatar)
         * 7. 🌈 動畫定義 (Animations)
         * 8. 📱 響應式設計 (Responsive)
         * 9. 🎨 主題色彩 (Theme Colors)
         * 10. 🔍 搜索優化 (Search Optimization)
         *
         * 🏷️ 搜索標籤: terminal, retro, amber, jellyfish, crt, neon
         * ======================================== */

        /* ========================================
         * 1. 🎯 CSS 變數定義 (CSS Variables) - RETRO UI v2.0 升級
         * ======================================== */
        :root {
            /* 🎨 主色系 - 增強磷光綠主題 (Enhanced Phosphor Green) */
            --color-primary: #00ff41;        /* 螢光綠 - 主要色彩 */
            --color-secondary: #39ff14;      /* 亮綠 - 次要色彩 */
            --color-accent: #00ffff;         /* 青色強調 */
            --color-background: #0a0a0a;     /* 深黑背景 - 護眼優化 */
            --color-error: #ff4757;          /* 錯誤紅 - 提升對比度 */

            /* 🌈 擴展色彩系統 - 2025 升級版 */
            --color-primary-amber: #ffa726;  /* 琥珀主色 - 提升亮度 */
            --color-secondary-amber: #ff8f00; /* 琥珀次色 */
            --color-tertiary-amber: #ffcc80; /* 琥珀三級 - 提升可讀性 */
            --color-muted-amber: #ff8a65;    /* 琥珀靜音 */
            --color-dark-amber: #e65100;     /* 琥珀深色 */

            /* 🌟 霓虹色彩 - 2025 增強版 */
            --color-neon-cyan: #00e5ff;     /* 青色霓虹 - 提升飽和度 */
            --color-neon-magenta: #e91e63;  /* 洋紅霓虹 - 護眼優化 */
            --color-neon-yellow: #ffeb3b;   /* 黃色霓虹 - 提升對比 */
            --color-neon-green: #00ff41;    /* 綠色霓虹 - 保持經典 */

            /* 🖤 背景色系 - 護眼深色優化 */
            --bg-primary: linear-gradient(135deg, #0a0a0a 0%, #121212 100%);
            --bg-secondary: linear-gradient(135deg, #000000 0%, #0a0a0a 100%);
            --bg-tertiary: rgba(0, 0, 0, 0.98); /* 提升不透明度 */

            /* 🔤 字體系統 - 2025 可讀性優化 */
            --font-mono: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
            --font-size-lg: 2rem;           /* 大標題 - 手機端優化 */
            --font-size-md: 1.3rem;         /* 中標題 - 提升可讀性 */
            --font-size-sm: 1.1rem;         /* 內文 - 手機端友好 */
            --font-size-xs: 0.9rem;         /* 小字 - 最小可讀尺寸 */

            /* 📏 間距系統 - HSN-45 標準化 */
            --space-xs: 0.25rem;
            --space-sm: 0.5rem;
            --space-md: 1rem;
            --space-lg: 1.5rem;
            --space-xl: 2rem;

            /* 保持向後兼容 */
            --spacing-xs: 4px;
            --spacing-sm: 8px;
            --spacing-md: 12px;
            --spacing-lg: 20px;
            --spacing-xl: 30px;

            /* ⏱️ 動畫時間 */
            --anim-fast: 0.3s;
            --anim-normal: 1s;
            --anim-slow: 3s;
            --anim-very-slow: 8s;

            /* 🎭 復古效果 - HSN-45 新增 */
            --glow-intensity: 0 0 8px var(--color-primary);
            --scan-line-opacity: 0.03;
            --terminal-border: 1px solid rgba(0, 255, 65, 0.1); /* 透明毛玻璃效果 */

            /* 🌟 陰影效果 */
            --shadow-glow-amber: 0 0 15px var(--color-primary-amber);
            --shadow-glow-strong: 0 0 25px rgba(255, 176, 0, 0.5), 0 0 35px rgba(255, 128, 0, 0.3);
            --shadow-glow-green: 0 0 15px var(--color-primary);
        }

        /* ========================================
         * 1.5 🎨 主題系統 (Theme System) - RETRO UI v2.0
         * ======================================== */

        /* 🟢 增強磷光綠主題 (預設) */
        [data-theme="retro"], [data-theme="enhanced-phosphor"] {
            --color-primary: #00ff41;
            --color-secondary: #39ff14;
            --color-accent: #00e5ff;
            --color-primary-amber: #ffa726;
            --color-secondary-amber: #ff8f00;
            --color-tertiary-amber: #ffcc80;
            --color-muted-amber: #ff8a65;
            --color-neon-green: #00ff41;
            --glow-intensity: 0 0 12px var(--color-primary);
        }

        /* 🌙 護眼深夜主題 - 低對比度護眼 */
        [data-theme="night-owl"] {
            --color-primary: #4fc3f7;        /* 柔和藍 */
            --color-secondary: #81c784;      /* 柔和綠 */
            --color-accent: #ffb74d;         /* 柔和橙 */
            --color-primary-amber: #ffb74d;  /* 暖橙 */
            --color-secondary-amber: #ff8a65; /* 柔和橙紅 */
            --color-tertiary-amber: #ffcc80; /* 淺橙 */
            --color-muted-amber: #a1887f;    /* 暖灰 */
            --color-neon-green: #81c784;
            --color-background: #0d1117;     /* GitHub 深色背景 */
            --glow-intensity: 0 0 6px var(--color-primary);
        }

        /* 🚀 賽博朋克紫主題 - 現代復古融合 */
        [data-theme="cyberpunk-purple"] {
            --color-primary: #bb86fc;        /* 賽博紫 */
            --color-secondary: #03dac6;      /* 賽博青 */
            --color-accent: #cf6679;         /* 賽博粉 */
            --color-primary-amber: #ffa726;  /* 琥珀橙 */
            --color-secondary-amber: #ff7043; /* 深橙 */
            --color-tertiary-amber: #ffcc80; /* 淺橙 */
            --color-muted-amber: #9c27b0;    /* 深紫 */
            --color-neon-green: #03dac6;
            --color-background: #121212;     /* Material 深色 */
            --glow-intensity: 0 0 15px var(--color-primary);
        }

        /* 💼 專業高對比主題 - 工作場景優化 */
        [data-theme="professional"] {
            --color-primary: #ffffff;        /* 純白 */
            --color-secondary: #e0e0e0;      /* 淺灰 */
            --color-accent: #2196f3;         /* 專業藍 */
            --color-primary-amber: #ff9800;  /* 專業橙 */
            --color-secondary-amber: #f57c00; /* 深橙 */
            --color-tertiary-amber: #ffcc80; /* 淺橙 */
            --color-muted-amber: #757575;    /* 中灰 */
            --color-neon-green: #4caf50;
            --color-background: #000000;     /* 純黑背景 */
            --glow-intensity: 0 0 8px var(--color-primary);
        }

        /* ========================================
         * 2. 🔧 基礎重置 (Base Reset)
         * ======================================== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;

            /* HSN-45 性能優化 */
            animation-fill-mode: both;
            animation-timing-function: ease-out;
        }

        /* 🚀 硬體加速優化 - HSN-45 */
        .terminal-container,
        .jellyfish-avatar,
        .chat-container,
        .mini-jellyfish-avatar {
            will-change: transform;
            transform: translateZ(0); /* 強制硬體加速 */
        }

        /* 🎯 減少重繪和重排 - HSN-45 */
        .crt-effect,
        .scan-lines::after,
        .chat-container::after {
            contain: layout style paint;
        }

        body {
            background: var(--bg-primary);
            color: var(--color-tertiary-amber);
            font-family: var(--font-mono);
            font-size: var(--font-size-base);
            line-height: 1.6;
            overflow-x: hidden;
            /* 復古風格：略微降低解析度效果 */
            image-rendering: pixelated;
            text-rendering: optimizeSpeed;
            filter: contrast(1.1) brightness(0.9);

            /* HSN-45 字體渲染優化 */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* ========================================
         * 3. 🖥️ 終端容器 (Terminal Container)
         * ======================================== */
        .terminal-container {
            /* 不再強制使用整個視窗高度 */
            padding: var(--spacing-lg);
            background: var(--bg-secondary);
            /* 為固定輸入區域預留底部空間 */
            padding-bottom: 180px;

            /* HSN-45 復古終端美學統一 - 透明毛玻璃效果 */
            border: var(--terminal-border);
            backdrop-filter: blur(10px);
            background: rgba(0, 0, 0, 0.2); /* 更透明的背景 */
            box-shadow:
                inset 0 0 20px rgba(0, 255, 65, 0.05),
                0 0 30px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow-x: hidden;
        }

        /* ========================================
         * 4. 📝 標題區域 (Header Section)
         * ======================================== */
        .terminal-header {
            border-bottom: 2px solid var(--color-primary-amber);
            border-image: linear-gradient(90deg,
                transparent,
                var(--color-primary-amber),
                var(--color-secondary-amber),
                var(--color-primary-amber),
                transparent
            ) 1;
            padding-bottom: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
            position: relative;
        }

        /* 🎨 哥德式裝飾線 - 淡化處理 */
        .terminal-header::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 50%;
            transform: translateX(-50%);
            width: 300px;
            height: 6px;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(0, 255, 65, 0.05) 10%,
                rgba(0, 255, 65, 0.02) 20%,
                rgba(0, 255, 65, 0.05) 30%,
                rgba(0, 255, 65, 0.02) 40%,
                rgba(0, 255, 65, 0.05) 50%,
                rgba(0, 255, 65, 0.02) 60%,
                rgba(0, 255, 65, 0.05) 70%,
                rgba(0, 255, 65, 0.02) 80%,
                rgba(0, 255, 65, 0.05) 90%,
                transparent 100%
            );
            box-shadow: none; /* 移除強烈陰影 */
            border-radius: 3px;
            opacity: 0.15; /* 大幅降低透明度 */
            z-index: -1; /* 確保在背景層 */
            filter: blur(0.5px); /* 輕微模糊 */
            animation: gothic-ornament var(--anim-slow) ease-in-out infinite;
            pointer-events: none; /* 避免影響文字選取 */
            user-select: none;
        }

        /* 🎮 8-bit 像素風裝飾 - 淡化處理 */
        .terminal-header::before {
            content: '▲ ■ ◆ ▼ ◆ ■ ▲';
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            color: rgba(0, 255, 65, 0.15); /* 大幅降低顏色強度 */
            font-size: var(--font-size-sm);
            letter-spacing: 6px;
            text-shadow: none; /* 移除發光效果 */
            animation: ornament-glow var(--anim-slow) ease-in-out infinite alternate;
            image-rendering: pixelated;
            opacity: 0.15; /* 降低透明度 */
            z-index: -1; /* 確保在背景層 */
            filter: blur(0.5px); /* 輕微模糊 */
            pointer-events: none; /* 避免影響文字選取 */
            user-select: none;
        }

        /* 📺 CRT 風格標題 */
        .terminal-title {
            font-size: 1.8rem; /* 謎之CLI 品牌升級 - 桌面版縮小 */
            font-weight: bold;
            text-align: center;
            margin-bottom: 10px;
            text-shadow: var(--shadow-glow-amber);
            color: var(--color-primary-amber);
            letter-spacing: 2px; /* 保持復古終端美學 */
            image-rendering: pixelated;
            filter: contrast(1.2) brightness(0.8);
            animation: crt-flicker var(--anim-slow) linear infinite alternate;
            position: relative;
        }

        /* 🔤 Preview 字體縮小 */
        .title-preview {
            font-size: 25%;
            opacity: 0.7;
            vertical-align: super;
        }

        /* 📺 CRT 掃描線效果 */
        .terminal-title::before {
            content: attr(data-text);
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                90deg,
                transparent 0%,
                rgba(255, 176, 0, 0.1) 50%,
                transparent 100%
            );
            animation: crt-scanline var(--anim-very-slow) linear infinite;
            pointer-events: none;
        }

        /* 📝 副標題樣式 */
        .terminal-subtitle {
            text-align: center;
            opacity: 0.8;
            font-size: var(--font-size-sm);
            color: #d4a574;
            line-height: 1.4;
            max-width: 600px;
            margin: 0 auto;
            image-rendering: pixelated;
            filter: contrast(1.2) brightness(0.85);
            animation: crt-flicker 4s linear infinite alternate;
        }

        /* 🌅 標題恢復提示樣式 (HSN-44) */
        .header-restore-hint {
            text-align: center;
            margin-bottom: 20px;
        }

        .restore-hint-text {
            font-size: var(--font-size-xs); /* 縮小字體 */
            color: var(--color-neon-green); /* 螢光綠 */
            opacity: 0.4; /* 更模糊 */
            font-style: italic;
            text-shadow: 0 0 4px rgba(0, 255, 65, 0.3); /* 模糊發光效果 */
            filter: blur(0.5px); /* 輕微模糊 */
            animation: gentle-pulse 2s ease-in-out infinite alternate;
        }

        @keyframes gentle-pulse {
            0% { opacity: 0.4; }
            100% { opacity: 0.8; }
        }

        /* 🌈 迷你水母緩慢變色動畫 */
        @keyframes slow-color-change {
            0% { filter: hue-rotate(0deg) saturate(1); }
            25% { filter: hue-rotate(90deg) saturate(1.2); }
            50% { filter: hue-rotate(180deg) saturate(0.8); }
            75% { filter: hue-rotate(270deg) saturate(1.1); }
            100% { filter: hue-rotate(360deg) saturate(1); }
        }

        /* 🌊 透明度脈衝動畫 */
        @keyframes opacity-pulse {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 0.9; }
        }

        /* ========================================
         * 5. 💬 聊天介面 (Chat Interface)
         * ======================================== */
        .chat-container {
            /* 不再佔據剩餘空間，使用固定尺寸 */
            max-width: 800px;
            margin: 0 auto 20px auto; /* 底部間距 */
            /* 🔲 隱形邊框加強化毛玻璃效果 */
            border: none; /* 隱形邊框 */
            border-radius: 8px;
            padding: var(--spacing-lg);
            background: rgba(0, 0, 0, 0.15); /* 更透明 */
            backdrop-filter: blur(8px); /* 加強毛玻璃 */

            /* HSN-45 復古終端美學統一 */
            position: relative;
            /* 確保聊天容器不會太高 */
            max-height: 70vh;
            overflow: visible;
        }

        /* HSN-45 聊天區域掃描線效果 */
        .chat-container::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                transparent 50%,
                rgba(0, 255, 65, var(--scan-line-opacity)) 50%
            );
            background-size: 100% 4px;
            pointer-events: none;
            border-radius: 8px;
        }

        .chat-history {
            /* 固定高度，避免需要滾動整個網頁 */
            height: 60vh; /* 固定為視窗高度的 60% */
            max-height: 500px; /* 最大高度限制 */
            overflow-y: auto;
            padding-right: 10px;
            /* 為固定輸入區域預留空間 */
            padding-bottom: 20px;
            margin-bottom: 20px;
            /* 確保聊天區域有明確的邊界 */
            border: 1px solid rgba(204, 153, 102, 0.1);
            border-radius: 8px;
        }

        .chat-message {
            margin-bottom: 15px;
            animation: fadeIn var(--anim-fast) ease-in;
        }

        /* 💬 訊息類型樣式 - 雙色內容顯示改善 */
        .message-user {
            color: var(--color-primary-amber);
            image-rendering: pixelated;
            filter: contrast(1.2) brightness(0.9);
        }

        .message-ai {
            color: var(--color-tertiary-amber);
            margin-left: var(--spacing-lg);
            border-left: 2px solid var(--color-primary-amber);
            padding-left: 15px;
            image-rendering: pixelated;
            filter: contrast(1.2) brightness(0.9);
        }

        /* 🎨 內容層次化顏色方案 */
        .content-hierarchy {
            /* 主要內容 - 終端綠 */
            color: #00ff41;
        }

        .content-hierarchy h1,
        .content-hierarchy h2,
        .content-hierarchy h3,
        .content-hierarchy .title,
        .content-hierarchy .heading {
            color: #c0c0c0; /* 較亮的灰色 */
            font-weight: 600;
            margin: 16px 0 8px 0;
        }

        .content-hierarchy .secondary-text,
        .content-hierarchy .timestamp,
        .content-hierarchy .metadata {
            color: #808080; /* 中等灰色 */
            font-size: 0.9em;
        }

        .content-hierarchy .highlight,
        .content-hierarchy .important {
            color: #00ffff; /* 青色突出 */
            font-weight: 500;
        }

        .content-hierarchy .search-result-title {
            color: #e0e0e0; /* 明亮灰色，提升可讀性 */
            font-weight: 600;
        }

        /* AI 回應中的標題層次 */
        .message-ai h1,
        .message-ai h2,
        .message-ai h3 {
            color: #d0d0d0;
            margin: 16px 0 8px 0;
            font-weight: 600;
        }

        .message-ai h1 {
            font-size: 1.3em;
            border-bottom: 1px solid rgba(208, 208, 208, 0.3);
            padding-bottom: 4px;
        }

        .message-ai h2 {
            font-size: 1.2em;
        }

        .message-ai h3 {
            font-size: 1.1em;
        }

        /* 強調和代碼塊 */
        .message-ai strong,
        .message-ai b {
            color: #00ffff;
            font-weight: 600;
        }

        .message-ai code {
            color: #00ff41;
            background: rgba(0, 255, 65, 0.1);
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }

        .message-ai pre {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(0, 255, 65, 0.2);
            border-radius: 6px;
            padding: 12px;
            margin: 12px 0;
            overflow-x: auto;
        }

        .message-ai pre code {
            background: none;
            padding: 0;
        }

        .message-system {
            color: #ffaa00;
            margin-left: 10px;
            border-left: 2px solid #ffaa00;
            padding-left: 10px;
            background: rgba(255, 170, 0, 0.1);
            border-radius: 4px;
        }

        /* 🧠 知識增強顯示 */
        .message-knowledge {
            color: var(--color-tertiary-amber);
            margin-left: 10px;
            border-left: 2px solid var(--color-primary-amber);
            padding: 15px;
            background: rgba(255, 176, 0, 0.03);
            border-radius: 6px;
            font-family: var(--font-mono);
            white-space: pre-wrap;
            font-size: 0.95rem;
            line-height: 1.7;
            border: 1px solid rgba(255, 176, 0, 0.15);
            image-rendering: auto;
            filter: none;
        }

        .message-knowledge .knowledge-header {
            color: #c0c0c0; /* 使用層次化標題色 */
            font-weight: bold;
            border-bottom: 1px solid rgba(192, 192, 192, 0.3);
            padding-bottom: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
            font-size: 1.1rem;
        }

        .message-knowledge .knowledge-section {
            margin-bottom: var(--spacing-md);
        }

        .message-knowledge .knowledge-result {
            background: rgba(255, 176, 0, 0.05);
            border-left: 3px solid var(--color-primary-amber);
            padding: var(--spacing-md);
            margin: var(--spacing-sm) 0;
            border-radius: 4px;
            border: 1px solid rgba(255, 176, 0, 0.1);
        }

        .message-knowledge .knowledge-result .result-title {
            color: #e0e0e0; /* 搜索結果標題使用明亮灰色 */
            font-weight: 600;
            font-size: 1.05em;
            margin-bottom: 8px;
            display: block;
        }

        .message-knowledge .knowledge-result .result-content {
            color: #00ff41; /* 主要內容使用終端綠 */
            line-height: 1.5;
        }

        .message-knowledge .knowledge-url {
            color: #808080; /* 次要信息使用中等灰色 */
            text-decoration: underline;
            opacity: 0.9;
            font-size: 0.9rem;
            display: block;
            margin-top: 8px;
        }

        .message-knowledge .knowledge-score {
            color: #00ffff; /* 強調信息使用青色 */
            font-weight: bold;
            font-size: 0.9rem;
        }

        /* 💻 輸入提示符 - 固定底部設計 */
        .prompt-line {
            display: flex;
            align-items: flex-start; /* 改為頂部對齊，適應多行輸入 */
            /* 改為 fixed 定位，確保精確控制位置 */
            position: fixed;
            bottom: 60px; /* 更靠近底部 */
            left: 0;
            right: 0;
            margin: 0;
            padding: calc(var(--spacing-md) * 2) var(--spacing-lg); /* 增加高度為兩倍 */
            background: var(--bg-secondary);
            border-radius: 8px 8px 0 0;
            border-top: 1px solid rgba(204, 153, 102, 0.2);
            z-index: 100;
            min-height: 80px; /* 設定最小高度 */
        }

        .prompt-symbol {
            color: var(--color-primary-amber);
            margin-right: 10px;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        /* 📱 輸入容器 - HSN-45 終端邊框設計 */
        .input-container {
            display: flex;
            align-items: flex-start; /* 改為頂部對齊，適應多行輸入 */
            gap: 8px;
            flex: 1;
            width: 100%;
            /* 為多行輸入提供更好的視覺效果 */
            background: rgba(0, 0, 0, 0.3);
            border: var(--terminal-border); /* HSN-45 終端邊框 */
            border-radius: 6px;
            padding: 8px;
            transition: background-color 0.2s ease, box-shadow 0.2s ease;
            position: relative;
        }

        .input-container:focus-within {
            background: rgba(0, 0, 0, 0.4); /* focus 時稍微加深背景 */
            box-shadow: 0 0 12px rgba(0, 255, 65, 0.2); /* 綠色微光效果 */
        }

        /* ========================================
         * 6. 🎭 水母頭像系統 (Jellyfish Avatar)
         * ======================================== */

        /* 🐙 2D 迷你水母頭像 - 緩慢變色透明版 */
        .mini-jellyfish-avatar {
            display: inline-block;
            width: 16px;
            height: 16px;
            position: relative;
            margin-right: var(--spacing-xs);

            /* 移除 3D 效果 */
            transform-style: flat;

            /* 緩慢變色和透明度變化 */
            animation:
                gentle-float 8s ease-in-out infinite,
                slow-color-change 12s ease-in-out infinite,
                opacity-pulse 6s ease-in-out infinite;

            /* 增加透明度 */
            opacity: 0.7;
        }

        .mini-sphere {
            position: absolute;
            border-radius: 50%;
            background: linear-gradient(135deg,
                rgba(0, 255, 65, 0.6) 0%,
                rgba(0, 255, 65, 0.3) 100%);
            border: 1px solid rgba(0, 255, 65, 0.4);

            /* 移除 3D 變換，使用 2D 效果 */
            animation: gentle-float 8s ease-in-out infinite;
        }

        .mini-sphere-1 {
            width: 14px;
            height: 14px;
            top: 1px;
            left: 1px;
            animation-delay: 0s;
        }

        .mini-sphere-2 {
            width: 10px;
            height: 10px;
            top: 3px;
            left: 3px;
            opacity: 0.7;
            animation-delay: 2.7s;
        }

        .mini-sphere-3 {
            width: 6px;
            height: 6px;
            top: 5px;
            left: 5px;
            opacity: 0.5;
            animation-delay: 5.4s;
        }

        .mini-core {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 3px;
            height: 3px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.9);
            box-shadow: 0 0 4px rgba(0, 255, 65, 0.6);
            animation: gentle-core-pulse 4s ease-in-out infinite;
        }

        /* 🌈 打字狀態顏色變化 */
        .typing-idle .mini-sphere {
            background: radial-gradient(circle at 30% 30%,
                rgba(255, 176, 0, 0.8) 0%,
                rgba(255, 128, 0, 0.6) 50%,
                transparent 100%);
        }
        .typing-command .mini-sphere {
            background: radial-gradient(circle at 30% 30%,
                rgba(255, 0, 128, 0.6) 0%,
                rgba(128, 0, 255, 0.4) 50%,
                transparent 100%);
        }
        .typing-short .mini-sphere {
            background: radial-gradient(circle at 30% 30%,
                rgba(0, 255, 255, 0.6) 0%,
                rgba(0, 128, 255, 0.4) 50%,
                transparent 100%);
        }
        .typing-medium .mini-sphere {
            background: radial-gradient(circle at 30% 30%,
                rgba(255, 255, 0, 0.6) 0%,
                rgba(255, 128, 0, 0.4) 50%,
                transparent 100%);
        }
        .typing-long .mini-sphere {
            background: radial-gradient(circle at 30% 30%,
                rgba(0, 255, 128, 0.6) 0%,
                rgba(128, 255, 0, 0.4) 50%,
                transparent 100%);
        }

        /* 🤖 AI 水母前綴 */
        .ai-jellyfish-prefix {
            display: inline-block;
            animation: ai-jellyfish-glow 12s ease-in-out infinite;
            filter: hue-rotate(0deg);
        }

        /* 👤 用戶水母前綴 */
        .user-jellyfish-prefix {
            display: inline-block;
            animation: user-jellyfish-glow 10s ease-in-out infinite;
            filter: hue-rotate(0deg);
        }

        /* ========================================
         * 7. 🌈 動畫定義 (Animations) - HSN-45 標準化
         * ======================================== */

        /* 🖥️ HSN-45 標準化復古效果 */

        /* CRT 螢幕效果 */
        .crt-effect {
            background-image:
                linear-gradient(transparent 50%, rgba(0, 255, 65, var(--scan-line-opacity)) 50%);
            background-size: 100% 4px;
            box-shadow:
                inset 0 0 20px rgba(0, 255, 65, 0.1),
                var(--glow-intensity);
        }

        /* 終端邊框樣式 */
        .terminal-border {
            border: var(--terminal-border);
            background: transparent;
            position: relative;
        }

        .terminal-border::before {
            content: "";
            position: absolute;
            top: -1px; left: -1px; right: -1px; bottom: -1px;
            background: linear-gradient(45deg, transparent, var(--color-primary), transparent);
            z-index: -1;
            opacity: 0.3;
        }

        /* 打字機效果標準 */
        @keyframes typewriter {
            from { width: 0; }
            to { width: 100%; }
        }

        @keyframes cursor-blink {
            0%, 50% { border-color: var(--color-primary); }
            51%, 100% { border-color: transparent; }
        }

        /* 掃描線效果 */
        .scan-lines {
            position: relative;
            overflow: hidden;
        }

        .scan-lines::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                transparent 50%,
                rgba(0, 255, 65, var(--scan-line-opacity)) 50%
            );
            background-size: 100% 4px;
            pointer-events: none;
        }

        /* 🤖 AI 水母霓虹發光動畫 */
        @keyframes ai-jellyfish-glow {
            0% {
                filter: hue-rotate(0deg) brightness(1.2);
                text-shadow: 0 0 8px var(--color-neon-cyan);
                color: var(--color-neon-cyan);
            }
            25% {
                filter: hue-rotate(90deg) brightness(1.4);
                text-shadow: 0 0 10px var(--color-neon-magenta);
                color: var(--color-neon-magenta);
            }
            50% {
                filter: hue-rotate(180deg) brightness(1.2);
                text-shadow: 0 0 8px var(--color-neon-yellow);
                color: var(--color-neon-yellow);
            }
            75% {
                filter: hue-rotate(270deg) brightness(1.4);
                text-shadow: 0 0 10px rgba(0, 255, 128, 1);
                color: #00ff80;
            }
            100% {
                filter: hue-rotate(360deg) brightness(1.2);
                text-shadow: 0 0 8px var(--color-neon-cyan);
                color: var(--color-neon-cyan);
            }
        }

        /* 👤 用戶水母琥珀發光動畫 */
        @keyframes user-jellyfish-glow {
            0% {
                filter: hue-rotate(0deg) brightness(1.1);
                text-shadow: 0 0 6px rgba(255, 204, 51, 1);
                color: #ffcc33;
            }
            25% {
                filter: hue-rotate(30deg) brightness(1.3);
                text-shadow: 0 0 8px var(--color-primary-amber);
                color: var(--color-primary-amber);
            }
            50% {
                filter: hue-rotate(60deg) brightness(1.1);
                text-shadow: 0 0 6px var(--color-secondary-amber);
                color: var(--color-secondary-amber);
            }
            75% {
                filter: hue-rotate(30deg) brightness(1.3);
                text-shadow: 0 0 8px var(--color-tertiary-amber);
                color: var(--color-tertiary-amber);
            }
            100% {
                filter: hue-rotate(0deg) brightness(1.1);
                text-shadow: 0 0 6px rgba(255, 204, 51, 1);
                color: #ffcc33;
            }
        }

        /* 🌊 2D 緩慢浮動動畫 */
        @keyframes gentle-float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.8;
            }
            25% {
                transform: translateY(-2px) rotate(1deg);
                opacity: 0.9;
            }
            50% {
                transform: translateY(-1px) rotate(0deg);
                opacity: 1;
            }
            75% {
                transform: translateY(-2px) rotate(-1deg);
                opacity: 0.9;
            }
        }

        /* 💓 緩慢核心脈衝動畫 */
        @keyframes gentle-core-pulse {
            0%, 100% {
                transform: translate(-50%, -50%) scale(1);
                opacity: 0.9;
            }
            50% {
                transform: translate(-50%, -50%) scale(1.1);
                opacity: 1;
            }
        }

        /* � 復古終端按鈕動畫 */
        @keyframes scan-lines {
            0% { background-position: 0 0; }
            100% { background-position: 4px 0; }
        }

        @keyframes loading-dots {
            0%, 20% { opacity: 1; }
            50% { opacity: 0.3; }
            80%, 100% { opacity: 1; }
        }

        /* Loading 狀態的點點動畫 */
        .mobile-send-button.loading {
            pointer-events: none;
            border-color: rgba(0, 255, 65, 0.6);
            color: rgba(0, 255, 65, 0.8);
        }

        .mobile-send-button.loading::after {
            content: '....';
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            font-size: 14px;
            letter-spacing: 2px;
            animation: loading-dots 1.5s ease-in-out infinite;
        }

        /* �📺 CRT 映像管螢幕效果動畫 */
        @keyframes crt-flicker {
            0% { opacity: 1; }
            99% { opacity: 1; }
            99.5% { opacity: 0.995; }
            100% { opacity: 1; }
        }

        @keyframes crt-scanline {
            0% { transform: translateY(-100%); }
            100% { transform: translateY(100vh); }
        }

        /* 🏛️ 哥德式裝飾動畫 */
        @keyframes gothic-ornament {
            0%, 100% {
                transform: translateX(-50%) scaleX(1);
                box-shadow: var(--shadow-glow-strong);
            }
            50% {
                transform: translateX(-50%) scaleX(1.1);
                box-shadow:
                    0 0 20px var(--color-primary-amber),
                    0 0 35px rgba(255, 176, 0, 0.7),
                    0 0 45px rgba(255, 128, 0, 0.5);
            }
        }

        @keyframes ornament-glow {
            0% {
                text-shadow: 0 0 10px var(--color-primary-amber);
                opacity: 0.8;
            }
            100% {
                text-shadow:
                    var(--shadow-glow-amber),
                    0 0 25px rgba(255, 176, 0, 0.8),
                    0 0 35px rgba(255, 128, 0, 0.6);
                opacity: 1;
            }
        }

        /* 🌊 哥德式流動效果 */
        @keyframes gothic-flow {
            0% {
                background: linear-gradient(90deg,
                    transparent 0%,
                    var(--color-secondary-amber) 10%,
                    var(--color-primary-amber) 25%,
                    var(--color-secondary-amber) 40%,
                    var(--color-primary-amber) 60%,
                    var(--color-secondary-amber) 75%,
                    var(--color-primary-amber) 90%,
                    transparent 100%
                );
            }
            50% {
                background: linear-gradient(90deg,
                    var(--color-primary-amber) 0%,
                    var(--color-secondary-amber) 15%,
                    var(--color-primary-amber) 30%,
                    var(--color-secondary-amber) 45%,
                    var(--color-primary-amber) 65%,
                    var(--color-secondary-amber) 80%,
                    var(--color-primary-amber) 95%,
                    var(--color-secondary-amber) 100%
                );
            }
            100% {
                background: linear-gradient(90deg,
                    transparent 0%,
                    var(--color-secondary-amber) 10%,
                    var(--color-primary-amber) 25%,
                    var(--color-secondary-amber) 40%,
                    var(--color-primary-amber) 60%,
                    var(--color-secondary-amber) 75%,
                    var(--color-primary-amber) 90%,
                    transparent 100%
                );
            }
        }

        /* 💻 輸入框樣式 - 多行輸入優化，隱藏原生游標 */
        .prompt-input {
            flex: 1;
            background: transparent;
            border: none;
            color: var(--color-primary-amber);
            font-family: inherit;
            font-size: inherit;
            outline: none;
            /* 原生遊標不閃爍使用背景色調 */
            caret-color: rgba(0, 0, 0, 0.3); /* 背景色調，不閃爍 */
            min-width: 0; /* 確保在 flex 容器中正確縮放 */
            width: 100%;
            /* 多行輸入優化 */
            min-height: 24px; /* 最小高度 */
            max-height: 120px; /* 最大高度，防止過度展開 */
            line-height: 1.4;
            padding: 4px 0;
            /* 確保文字完全可見 */
            overflow-y: auto;
            resize: none;
        }

        .prompt-input::placeholder {
            color: #666666;
            opacity: 0.8;
        }

        /* 📱 移動端發送按鈕 - 復古終端升級 */
        .mobile-send-button {
            /* 復古終端美感 */
            background: transparent;
            border: 2px solid #00ff41;
            color: #00ff41;
            padding: 8px 16px;
            border-radius: 0; /* 移除圓角，更復古 */
            font-family: 'Courier New', monospace;
            font-weight: bold;
            font-size: 11px;
            letter-spacing: 1px; /* 增加字母間距 */
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 70px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            white-space: nowrap;

            /* 終端風格效果 */
            position: relative;
            z-index: 10;
            text-transform: uppercase;
            box-shadow:
                inset 0 0 0 1px rgba(0, 255, 65, 0.3),
                0 0 10px rgba(0, 255, 65, 0.2);

            /* 復古掃描線效果 */
            background-image:
                linear-gradient(
                    90deg,
                    transparent 50%,
                    rgba(0, 255, 65, 0.03) 50%
                );
            background-size: 4px 100%;
        }

        .mobile-send-button:hover:not(:disabled) {
            /* 復古終端 hover 效果 */
            background: rgba(0, 255, 65, 0.1);
            border-color: #00ff65;
            color: #00ff65;
            box-shadow:
                inset 0 0 0 1px rgba(0, 255, 65, 0.5),
                0 0 15px rgba(0, 255, 65, 0.4),
                0 0 25px rgba(0, 255, 65, 0.2);
            text-shadow: 0 0 8px rgba(0, 255, 65, 0.6);

            /* 掃描線動畫 */
            background-image:
                linear-gradient(
                    90deg,
                    transparent 50%,
                    rgba(0, 255, 65, 0.08) 50%
                );
            animation: scan-lines 0.1s linear infinite;
        }

        .mobile-send-button:active:not(:disabled) {
            /* 復古按下效果 */
            background: rgba(0, 255, 65, 0.2);
            border-color: #00ff41;
            box-shadow:
                inset 0 0 0 2px rgba(0, 255, 65, 0.6),
                0 0 8px rgba(0, 255, 65, 0.3);
            transform: scale(0.98);
        }

        .mobile-send-button:disabled {
            /* 禁用狀態 */
            opacity: 0.4;
            cursor: not-allowed;
            background: transparent;
            border-color: rgba(0, 255, 65, 0.3);
            color: rgba(0, 255, 65, 0.3);
            box-shadow: none;
            text-shadow: none;
        }

        /* ========================================
         * 📱 手機端可讀性優化 - RETRO UI v2.0
         * ======================================== */
        @media (max-width: 768px) {
            /* 🔤 字體大小優化 - 提升可讀性 */
            :root {
                --font-size-lg: 2.2rem;     /* 大標題 - 手機端加大 */
                --font-size-md: 1.4rem;     /* 中標題 - 提升可讀性 */
                --font-size-sm: 1.2rem;     /* 內文 - 手機端友好 */
                --font-size-xs: 1rem;       /* 小字 - 最小可讀尺寸 */
            }

            /* 📱 發送按鈕優化 */
            .mobile-send-button {
                display: flex;
                padding: 14px 18px;         /* 增加觸控區域 */
                min-width: 80px;
                height: 44px;               /* 符合 iOS 最小觸控標準 */
                font-size: 13px;            /* 提升字體大小 */
                /* 確保按鈕與多行輸入框對齊 */
                align-self: flex-end;
                margin-bottom: 2px;
                /* 置底設計的視覺增強 */
                border-width: 2px;
                box-shadow:
                    inset 0 0 0 1px rgba(0, 255, 65, 0.3),
                    0 0 10px rgba(0, 255, 65, 0.15);
            }

            /* 📝 輸入框可讀性優化 */
            .prompt-input {
                font-size: 16px;            /* 防止 iOS 縮放 */
                /* 行動端輸入框優化 */
                min-height: 32px;           /* 增加高度 */
                line-height: 1.4;           /* 提升行距 */
                padding: 8px 0;             /* 增加內邊距 */
            }

            /* 💬 聊天訊息可讀性優化 */
            .message-user, .message-ai {
                font-size: 1.1rem;          /* 提升聊天字體 */
                line-height: 1.6;           /* 增加行距 */
                margin-bottom: 18px;        /* 增加間距 */
            }

            /* 📺 標題可讀性優化 */
            .terminal-title {
                font-size: 2rem;            /* 手機端標題加大 */
                line-height: 1.3;
            }

            .terminal-subtitle {
                font-size: 1.1rem;          /* 副標題加大 */
                line-height: 1.5;
            }

            .input-container {
                gap: 10px;
                /* 行動端輸入容器優化 - 置底設計，加強毛玻璃 */
                padding: 12px;
                border-radius: 8px 8px 0 0; /* 只有上方圓角 */
                border-bottom: none; /* 移除底部邊框 */
                background: rgba(0, 0, 0, 0.4); /* 稍微透明以顯示毛玻璃效果 */
                backdrop-filter: blur(15px) saturate(150%); /* 大幅加強毛玻璃效果 */
                -webkit-backdrop-filter: blur(15px) saturate(150%); /* Safari 支援 */
                box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2); /* 增加陰影 */
            }

            .prompt-line {
                /* 行動端完全置底設計 - 加強毛玻璃效果 */
                position: fixed;
                bottom: 100px; /* 大幅增加間距，確保不擋住訊息 */
                left: 0;
                right: 0;
                margin: 0;
                padding: var(--spacing-sm) var(--spacing-md);
                background: rgba(0, 0, 0, 0.85); /* 稍微透明以顯示毛玻璃效果 */
                backdrop-filter: blur(20px) saturate(180%); /* 大幅加強毛玻璃效果 */
                -webkit-backdrop-filter: blur(20px) saturate(180%); /* Safari 支援 */
                border-top: 1px solid rgba(0, 255, 65, 0.2); /* 改為綠色邊框 */
                box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3); /* 增加陰影深度 */
                z-index: 200; /* 確保在最上層 */
            }

            .chat-history {
                /* 行動端聊天歷史大幅擴展 */
                max-height: calc(100vh - 200px - 120px); /* 大幅增加顯示區域 */
                padding-bottom: 140px; /* 為固定輸入區預留空間 */
                margin-bottom: 0; /* 移除底部邊距 */
            }

            .terminal-container {
                /* 行動端容器調整 */
                padding-bottom: 160px; /* 為固定輸入區預留更多空間 */
            }
        }

        @media (min-width: 769px) {
            .mobile-send-button {
                display: none; /* 桌面版隱藏，保持鍵盤操作 */
            }
        }

        /* � 狀態列行動端優化 */
        @media (max-width: 768px) {
            .status-bar {
                /* 行動端字體和間距優化 */
                font-size: 11px;
                padding: 8px var(--spacing-md);
                height: 50px; /* 固定高度 */

                /* 確保橫向顯示 */
                writing-mode: horizontal-tb !important;
                text-orientation: mixed !important;
                white-space: nowrap !important;

                /* 防止文字換行 */
                flex-wrap: nowrap;
                overflow: hidden;
            }

            .status-bar > * {
                /* 狀態列內容強制橫向 */
                writing-mode: horizontal-tb !important;
                text-orientation: mixed !important;
                white-space: nowrap !important;
                display: inline-block;
                vertical-align: middle;
            }

            .status-bar .status-left,
            .status-bar .status-right {
                /* 左右區域優化 */
                writing-mode: horizontal-tb !important;
                white-space: nowrap !important;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 45%; /* 限制寬度防止重疊 */
            }
        }

        /* �🔸 終端游標 */
        .cursor {
            display: inline-block;
            width: 10px;
            height: 20px;
            background: var(--color-neon-green);
            animation: blink var(--anim-normal) infinite;
        }

        /* 🔹 極簡小游標 - 螢光綠縮小版 */
        .mini-cursor {
            display: inline-block;
            width: 4px; /* 縮窄為50% */
            height: 12px; /* 減小高度 */
            background: var(--color-neon-green); /* 螢光綠 */
            animation: retro-cursor-blink 1s ease-in-out infinite;
            margin-left: 4px;
            vertical-align: middle;
            box-shadow: 0 0 6px rgba(0, 255, 65, 0.6); /* 螢光綠發光 */
        }

        /* 復古終端游標閃爍動畫 - 螢光綠 */
        @keyframes retro-cursor-blink {
            0%, 50% {
                opacity: 1;
                background: var(--color-neon-green);
                box-shadow: 0 0 8px rgba(0, 255, 65, 0.8);
            }
            51%, 100% {
                opacity: 0;
                background: transparent;
                box-shadow: none;
            }
        }

        /* 📊 狀態欄 - 行動端橫向顯示修復 */
        .status-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(135deg, #1a1a1a 0%, #2a1a0a 100%);
            border-top: 2px solid var(--color-primary-amber);
            border-image: linear-gradient(90deg,
                var(--color-secondary-amber),
                var(--color-primary-amber),
                var(--color-secondary-amber)
            ) 1;
            padding: 10px var(--spacing-lg);
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: var(--font-size-sm);
            color: #d4a574;
            z-index: 1000;
            box-shadow: 0 -5px 20px rgba(255, 176, 0, 0.1);

            /* 強制橫向顯示 */
            writing-mode: horizontal-tb;
            text-orientation: mixed;
            white-space: nowrap;
            overflow: hidden;
        }

        /* 🌊 狀態欄流動裝飾 - 淡化處理 */
        .status-bar::before {
            content: '';
            position: absolute;
            top: -4px;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(0, 255, 65, 0.05) 5%,
                rgba(0, 255, 65, 0.02) 15%,
                rgba(0, 255, 65, 0.05) 25%,
                rgba(0, 255, 65, 0.02) 35%,
                rgba(0, 255, 65, 0.05) 45%,
                rgba(0, 255, 65, 0.02) 55%,
                rgba(0, 255, 65, 0.05) 65%,
                rgba(0, 255, 65, 0.02) 75%,
                rgba(0, 255, 65, 0.05) 85%,
                rgba(0, 255, 65, 0.02) 95%,
                transparent 100%
            );
            box-shadow: none; /* 移除發光效果 */
            opacity: 0.15; /* 降低透明度 */
            z-index: -1; /* 確保在背景層 */
            filter: blur(0.5px); /* 輕微模糊 */
            animation: gothic-flow 4s linear infinite;
            pointer-events: none; /* 避免影響文字選取 */
            user-select: none;
        }

        /* 🎮 8-bit 狀態欄裝飾 - 已淡化 */
        .status-bar::after {
            content: '▪ ▫ ▪ ▫ ▪';
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            color: rgba(0, 255, 65, 0.05); /* 進一步降低顏色強度 */
            font-size: var(--font-size-xs);
            letter-spacing: 4px;
            text-shadow: none; /* 移除發光效果 */
            animation: ornament-glow 4s ease-in-out infinite alternate;
            opacity: 0.05; /* 保持極低透明度 */
            image-rendering: pixelated;
            z-index: -1; /* 確保在背景層 */
            filter: blur(0.5px); /* 輕微模糊 */
            pointer-events: none; /* 避免影響文字選取 */
            user-select: none;
        }

        /* 🎯 內容區域優先級 */
        .content-area {
            position: relative;
            z-index: 1;
            background: rgba(0, 0, 0, 0.85); /* 輕微背景，提升可讀性 */
            border-radius: 4px;
            padding: 8px;
        }

        .chat-history,
        .terminal-header,
        .prompt-line,
        .status-bar {
            position: relative;
            z-index: 2; /* 確保內容在裝飾之上 */
        }

        /* 🔗 可點擊標題 */
        .clickable-title {
            cursor: pointer;
            color: var(--color-primary-amber);
            transition: all var(--anim-fast) ease;
            padding: 2px 6px;
            border-radius: 3px;
            max-width: 400px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            border: 1px solid transparent;
        }

        .clickable-title:hover {
            background: rgba(255, 176, 0, 0.1);
            color: var(--color-secondary-amber);
            transform: scale(1.02);
            border: 1px solid rgba(255, 176, 0, 0.3);
            box-shadow: 0 0 10px rgba(255, 176, 0, 0.2);
        }

        /* 🔄 載入狀態 */
        .loading {
            opacity: 0.6;
        }

        /* 💾 草稿保存提示 */
        .draft-saved-notice {
            position: fixed;
            top: var(--spacing-lg);
            right: var(--spacing-lg);
            background: rgba(204, 153, 102, 0.9);
            color: #fff;
            padding: var(--spacing-sm) 16px;
            border-radius: 4px;
            font-size: var(--font-size-sm);
            z-index: 1000;
            animation: fadeInOut var(--anim-slow) ease-in-out;
        }

        /* 🎬 淡入淡出動畫 */
        @keyframes fadeInOut {
            0% { opacity: 0; transform: translateY(-10px); }
            20% { opacity: 1; transform: translateY(0); }
            80% { opacity: 1; transform: translateY(0); }
            100% { opacity: 0; transform: translateY(-10px); }
        }

        /* 🚨 狀態顏色 */
        .error {
            color: #ff4141;
        }

        .success {
            color: #41ff41;
        }

        /* 🎭 基礎動畫效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        /* ========================================
         * 8. 📱 響應式設計 (Responsive) - HSN-45 優化
         * ======================================== */

        /* 🔧 基礎響應式設置 */
        .terminal-container {
            max-width: 1200px; /* 桌面版最大寬度限制 */
            margin: 0 auto;
        }

        /* 📱 手機版 (< 480px) - 小手機 */
        @media (max-width: 479px) {
            .terminal-container {
                padding: var(--space-sm);
            }

            .terminal-title {
                font-size: 1.2rem;
                letter-spacing: 0.5px;
            }

            .terminal-subtitle {
                font-size: var(--font-size-xs);
                line-height: 1.3;
            }

            /* 觸控區域最小 44px */
            .jellyfish-avatar {
                width: 44px;
                height: 44px;
                min-width: 44px;
                min-height: 44px;
            }

            /* 水母動畫性能優化 */
            .jellyfish-avatar * {
                animation-duration: 4s; /* 減慢動畫提升性能 */
            }
        }

        /* 📱 大手機版 (480px - 767px) */
        @media (min-width: 480px) and (max-width: 767px) {
            .terminal-container {
                padding: var(--space-md);
            }

            .terminal-title {
                font-size: 1.4rem; /* 謎之CLI 品牌升級 - 手機版 */
                letter-spacing: 1px; /* 手機版字距調整 */
            }

            .terminal-subtitle {
                font-size: var(--font-size-sm);
            }

            .jellyfish-avatar {
                width: 48px;
                height: 48px;
            }
        }

        /* 📟 平板版 (768px - 1023px) */
        @media (min-width: 768px) and (max-width: 1023px) {
            .terminal-container {
                padding: var(--space-lg);
            }

            .terminal-title {
                font-size: 1.6rem;
                letter-spacing: 1.5px;
            }

            .terminal-subtitle {
                font-size: var(--font-size-md);
            }

            /* 佈局過渡優化 */
            .chat-container {
                transition: all var(--anim-fast) ease;
            }

            /* 保持桌面版功能 */
            .jellyfish-avatar {
                width: 50px;
                height: 50px;
            }
        }

        /* 🖥️ 桌面版 (1024px - 1439px) */
        @media (min-width: 1024px) and (max-width: 1439px) {
            .terminal-title {
                font-size: 1.8rem; /* HSN-44 標準 */
                letter-spacing: 2px;
            }

            .terminal-subtitle {
                font-size: var(--font-size-md);
            }

            /* 視覺層次強化 */
            .terminal-header {
                margin-bottom: var(--space-xl);
            }
        }

        /* 🖥️ 大螢幕 (> 1440px) */
        @media (min-width: 1440px) {
            .terminal-container {
                max-width: 1400px;
                padding: var(--space-xl);
            }

            .terminal-title {
                font-size: 2rem;
                letter-spacing: 2.5px;
            }

            /* 高解析度支持 */
            .crt-effect {
                background-size: 100% 2px; /* 更細的掃描線 */
            }
        }

        /* 🎚️ 復古風格滾動條 */
        .chat-history::-webkit-scrollbar {
            width: 12px;
        }

        .chat-history::-webkit-scrollbar-track {
            background: linear-gradient(
                180deg,
                #1a1a1a 0%,
                #1f1f1f 50%,
                #1a1a1a 100%
            );
            border-radius: 6px;
            border: 1px solid rgba(204, 153, 102, 0.1);
        }

        .chat-history::-webkit-scrollbar-thumb {
            background: linear-gradient(
                180deg,
                rgba(230, 163, 102, 0.6) 0%,
                rgba(204, 153, 102, 0.4) 50%,
                rgba(230, 163, 102, 0.6) 100%
            );
            border-radius: 6px;
            border: 1px solid rgba(230, 163, 102, 0.2);
            box-shadow:
                inset 0 0 2px rgba(230, 163, 102, 0.3),
                0 0 3px rgba(230, 163, 102, 0.2);
        }

        .chat-history::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(
                180deg,
                #ffd700 0%,
                var(--color-primary-amber) 25%,
                var(--color-secondary-amber) 50%,
                var(--color-primary-amber) 75%,
                #ffd700 100%
            );
            box-shadow:
                inset 0 0 5px rgba(255, 215, 0, 0.7),
                0 0 8px rgba(255, 176, 0, 0.5);
        }

        /* 🐙 3D 透明水母霓虹燈頭像系統 */
        .jellyfish-avatar {
            width: 50px;
            height: 50px;
            position: relative;
            margin: 0 auto var(--spacing-lg);
            cursor: pointer;
            perspective: 200px;
            transform-style: preserve-3d;
            transition: transform 0.3s ease, filter 0.3s ease;
        }

        /* 🌅 水母 hover 效果 (HSN-44) */
        .jellyfish-avatar:hover {
            transform: scale(1.05);
            filter: brightness(1.2) saturate(1.3);
        }

        /* 🎨 1980s 極簡主題切換圓點 */
        .theme-switcher {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .theme-dot {
            width: 12px;
            height: 12px;
            border: 1px solid var(--color-primary);
            border-radius: 50%;
            background: transparent;
            transition: all 0.3s ease;
            position: relative;
        }

        .theme-dot::before {
            content: "";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: var(--color-primary);
            opacity: 0.3;
            transition: all 0.3s ease;
        }

        .theme-switcher:hover .theme-dot {
            transform: scale(1.2);
            box-shadow: 0 0 8px var(--color-primary);
        }

        .theme-switcher:hover .theme-dot::before {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1.2);
        }

        .jellyfish-sphere {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            transform-style: preserve-3d;
        }

        .sphere-layer {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            border: 1px solid transparent;
            background: radial-gradient(
                circle at 30% 30%,
                rgba(255, 0, 128, 0.3) 0%,
                rgba(0, 255, 255, 0.2) 25%,
                rgba(255, 255, 0, 0.2) 50%,
                rgba(255, 128, 0, 0.2) 75%,
                transparent 100%
            );
            animation: sphere-wave var(--anim-very-slow) ease-in-out infinite;
        }

        .layer-1 {
            transform: translateZ(0px) scale(1);
            background: radial-gradient(
                circle at 30% 30%,
                rgba(255, 0, 128, 0.4) 0%,
                rgba(128, 0, 255, 0.3) 50%,
                transparent 100%
            );
            animation-delay: 0s;
        }

        .layer-2 {
            transform: translateZ(-10px) scale(0.9);
            background: radial-gradient(
                circle at 70% 40%,
                rgba(0, 255, 255, 0.4) 0%,
                rgba(0, 255, 128, 0.3) 50%,
                transparent 100%
            );
            animation-delay: 1s;
        }

        .layer-3 {
            transform: translateZ(-20px) scale(0.8);
            background: radial-gradient(
                circle at 50% 70%,
                rgba(255, 255, 0, 0.4) 0%,
                rgba(255, 128, 0, 0.3) 50%,
                transparent 100%
            );
            animation-delay: 2s;
        }

        .layer-4 {
            transform: translateZ(-30px) scale(0.7);
            background: radial-gradient(
                circle at 40% 60%,
                rgba(255, 128, 0, 0.4) 0%,
                rgba(255, 0, 128, 0.3) 50%,
                transparent 100%
            );
            animation-delay: 3s;
        }

        .jellyfish-tentacles {
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            width: 80%;
            height: 40px;
        }

        .tentacle {
            position: absolute;
            width: 1px;
            height: 100%;
            background: linear-gradient(
                to bottom,
                rgba(255, 0, 128, 0.3) 0%, /* 降低透明度，更不明顯 */
                rgba(0, 255, 255, 0.2) 50%,
                transparent 100%
            );
            animation: tentacle-wave 6s ease-in-out infinite; /* 慢一倍 */
            transform-origin: top center;
        }

        .tentacle-1 { left: 10%; animation-delay: 0s; }
        .tentacle-2 { left: 25%; animation-delay: 0.5s; }
        .tentacle-3 { left: 40%; animation-delay: 1s; }
        .tentacle-4 { left: 55%; animation-delay: 1.5s; }
        .tentacle-5 { left: 70%; animation-delay: 2s; }
        .tentacle-6 { left: 85%; animation-delay: 2.5s; }

        .jellyfish-core {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: radial-gradient(
                circle,
                rgba(255, 255, 255, 0.8) 0%,
                rgba(255, 176, 0, 0.6) 30%,
                rgba(255, 128, 0, 0.4) 70%,
                transparent 100%
            );
            box-shadow: 0 0 15px rgba(255, 176, 0, 0.8);
        }

        .core-pulse {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.9);
            animation: core-pulse 2s ease-in-out infinite;
        }

        /* 水母狀態指示系統 */
        .avatar-state-idle .sphere-layer {
            animation-duration: 8s; /* 更慢的基礎速度 */
        }

        .avatar-state-thinking .sphere-layer {
            animation-duration: 6s; /* 思考時稍快 */
        }

        .avatar-state-discovering .sphere-layer {
            animation-duration: 4s; /* 發現時中等速度 */
        }

        .avatar-state-thinking .tentacle {
            animation-duration: 5s; /* 觸手也更慢 */
        }

        .avatar-state-discovering .tentacle {
            animation-duration: 3s;
        }

        /* 水母動畫定義 */
        @keyframes sphere-wave {
            0%, 100% {
                transform: translateZ(var(--layer-z, 0px)) scale(var(--layer-scale, 1)) rotateY(0deg);
                opacity: 0.6;
            }
            25% {
                transform: translateZ(var(--layer-z, 0px)) scale(calc(var(--layer-scale, 1) * 1.1)) rotateY(90deg);
                opacity: 0.8;
            }
            50% {
                transform: translateZ(var(--layer-z, 0px)) scale(var(--layer-scale, 1)) rotateY(180deg);
                opacity: 0.6;
            }
            75% {
                transform: translateZ(var(--layer-z, 0px)) scale(calc(var(--layer-scale, 1) * 0.9)) rotateY(270deg);
                opacity: 0.4;
            }
        }

        @keyframes tentacle-wave {
            0%, 100% {
                transform: rotate(0deg) scaleY(1);
                opacity: 0.6;
            }
            25% {
                transform: rotate(5deg) scaleY(1.2);
                opacity: 0.8;
            }
            50% {
                transform: rotate(-3deg) scaleY(0.8);
                opacity: 0.4;
            }
            75% {
                transform: rotate(2deg) scaleY(1.1);
                opacity: 0.7;
            }
        }

        @keyframes core-pulse {
            0%, 100% {
                transform: scale(1);
                opacity: 0.9;
                box-shadow: var(--shadow-glow-amber);
            }
            50% {
                transform: scale(1.3);
                opacity: 1;
                box-shadow:
                    0 0 25px var(--color-primary-amber),
                    0 0 35px rgba(255, 128, 0, 0.8);
            }
        }

        /* ========================================
         * 9. 🎨 主題色彩 (Theme Colors)
         * ======================================== */

        /* 🌈 個性化用戶色彩系統 */
        .user-color-1 {
            --user-color-primary: var(--color-neon-green);
            --user-color-secondary: #41ff00;
        }

        .user-color-2 {
            --user-color-primary: #ff6600;
            --user-color-secondary: #ff9900;
        }

        .user-color-3 {
            --user-color-primary: var(--color-neon-cyan);
            --user-color-secondary: #0099ff;
        }

        .user-color-4 {
            --user-color-primary: var(--color-neon-magenta);
            --user-color-secondary: #ff66ff;
        }

        .user-color-5 {
            --user-color-primary: var(--color-neon-yellow);
            --user-color-secondary: #ffcc00;
        }

        /* ========================================
         * 10. 🔍 搜索優化 (Search Optimization)
         * ======================================== */

        /* 🏷️ 搜索標籤和關鍵詞 */
        /*
         * 搜索關鍵詞索引:
         * - terminal, retro, amber, crt, neon
         * - jellyfish, avatar, 3d, transparent
         * - chat, ai, knowledge, tavily
         * - gothic, ornament, pixel, 8bit
         * - responsive, mobile, desktop
         * - animation, glow, pulse, wave
         * - scrollbar, status, input, prompt
         * - css-variables, modular, organized
         */

        /* 🧪 導航連結樣式 */
        .nav-links {
            display: flex;
            gap: var(--spacing-md);
            margin-top: var(--spacing-lg);
            justify-content: center;
            flex-wrap: wrap;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: rgba(255, 204, 51, 0.1);
            border: 1px solid rgba(255, 204, 51, 0.3);
            border-radius: 6px;
            color: var(--color-primary-amber);
            text-decoration: none;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }

        .nav-link:hover {
            background: rgba(255, 204, 51, 0.2);
            border-color: var(--color-primary-amber);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 204, 51, 0.3);
        }

        /* * 主題切換按鈕極簡樣式 */
        .theme-switcher {
            background: transparent !important;
            border: 1px solid var(--color-secondary) !important;
            color: var(--color-secondary) !important;
            cursor: pointer;
            transition: opacity 0.2s ease;
        }

        .theme-switcher:hover {
            opacity: 0.7;
            border-color: var(--color-primary);
            color: var(--color-primary);
        }

        .nav-icon {
            font-size: 14px;
            filter: drop-shadow(0 0 4px currentColor);
        }

        .nav-text {
            font-family: var(--font-mono);
            letter-spacing: 0.5px;
        }

        /* 極簡化導航 - 隱藏文字，只顯示符號 */
        .nav-text {
            display: none;
        }

        .nav-link {
            padding: 8px;
            min-width: 32px;
            justify-content: center;
        }

        /* 響應式調整 */
        @media (max-width: 768px) {
            .nav-links {
                gap: var(--spacing-sm);
                margin-top: var(--spacing-md);
            }

            .nav-link {
                padding: 6px;
                font-size: 11px;
                min-width: 28px;
            }
        }

        /* ========================================
         * 🎨 主題切換動畫 - RETRO UI v2.0
         * ======================================== */
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        /* 主題切換過渡效果 */
        * {
            transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
        }

        /* ========================================
         * * 螢幕保護模式 - 簡化淡化效果
         * ======================================== */
        .screen-saver-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.9);
            z-index: 10000;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(5px);
        }

        .screen-saver-overlay::before {
            content: '~ SCREEN SAVER ~';
            color: var(--color-secondary);
            font-family: var(--font-mono);
            font-size: 28px;
            letter-spacing: 0.2em;
            text-shadow: 0 0 6px rgba(0,255,65,0.25), 0 0 12px rgba(0,255,65,0.15);
            opacity: 0.35;
            animation: fadeInOut 3s ease-in-out infinite;
        }

        @keyframes fadeInOut {
            0%, 100% { opacity: 0.1; }
            50% { opacity: 0.5; }
        }

        /* * 螢幕保護按鈕 - 與主題切換按鈕一致的樣式 */
        .screen-saver-btn {
            position: absolute;
            top: 15px;
            left: 15px;
            padding: 8px;
            min-width: 32px;
            height: auto;
            background: transparent;
            border: 1px solid var(--color-secondary);
            color: var(--color-secondary);
            font-family: var(--font-mono);
            font-size: 12px;
            cursor: pointer;
            z-index: 100;
            transition: opacity 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .screen-saver-btn:hover {
            opacity: 0.7;
            border-color: var(--color-primary);
            color: var(--color-primary);
        }

        /* 🎨 1980s 極簡主題切換圓點 */
        .theme-switcher {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .theme-dot {
            width: 12px;
            height: 12px;
            border: 1px solid var(--color-primary);
            border-radius: 50%;
            background: transparent;
            transition: all 0.3s ease;
            position: relative;
        }

        .theme-dot::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: var(--color-primary);
            opacity: 0.3;
            transition: all 0.3s ease;
        }

        .theme-switcher:hover .theme-dot {
            transform: scale(1.2);
            box-shadow: 0 0 8px var(--color-primary);
        }

        .theme-switcher:hover .theme-dot::before {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1.2);
        }
    </style>
</head>
<body>

    <!-- 🖥️ Alpine.js 終端應用容器 -->
    <div x-data="hsnCliApp()" class="terminal-container">

        <!-- 終端標題 -->
        <header class="terminal-header">


            <!-- 3D 透明水母霓虹燈頭像 -->
            <div class="jellyfish-avatar"
                 :class="[\`avatar-state-\${avatarState}\`]"
                 @click="onJellyfishClick()"
                 :title="headerVisible ? '點擊切換頭像狀態' : '點擊恢復標題'"
                 style="cursor: pointer;">
                <div class="jellyfish-sphere">
                    <div class="sphere-layer layer-1"></div>
                    <div class="sphere-layer layer-2"></div>
                    <div class="sphere-layer layer-3"></div>
                    <div class="sphere-layer layer-4"></div>
                </div>
                <div class="jellyfish-tentacles">
                    <div class="tentacle tentacle-1"></div>
                    <div class="tentacle tentacle-2"></div>
                    <div class="tentacle tentacle-3"></div>
                    <div class="tentacle tentacle-4"></div>
                    <div class="tentacle tentacle-5"></div>
                    <div class="tentacle tentacle-6"></div>
                </div>
                <div class="jellyfish-core">
                    <div class="core-pulse"></div>
                </div>
            </div>

            <!-- 🌅 標題淡化系統 (HSN-44) -->
            <div x-show="headerVisible"
                 x-transition:enter="transition ease-out duration-1000"
                 x-transition:enter-start="opacity-0 scale-95"
                 x-transition:enter-end="opacity-100 scale-100"
                 x-transition:leave="transition ease-in-out duration-2000"
                 x-transition:leave-start="opacity-100"
                 x-transition:leave-end="opacity-0">
                <h1 class="terminal-title">謎之CLI <span class="title-preview">Preview</span></h1>
                <p class="terminal-subtitle" @click="toggleSubtitleEnglish()">
                    <span>終端式低干擾數位生活與社交平台</span>
                    <span x-show="showEnglishSubtitle" x-transition> | Terminal-based Low-interference Digital Life & Social Platform</span>
                </p>
            </div>

            <!-- 🌅 標題隱藏時的恢復提示 (HSN-44) -->
            <div x-show="!headerVisible"
                 x-transition:enter="transition ease-in duration-1000"
                 x-transition:enter-start="opacity-0"
                 x-transition:enter-end="opacity-100"
                 class="header-restore-hint">
                <p class="restore-hint-text">點擊水母恢復標題</p>
            </div>

        </header>

        <!-- 🎨 1980s 極簡主題切換圓點 -->
        <div class="theme-switcher"
             @click="cycleTheme()"
             :title="'當前主題: ' + themeNames[currentTheme] + ' (點擊切換)'">
            <div class="theme-dot"></div>
        </div>



        <!-- 聊天容器 -->
        <main class="chat-container">

            <!-- 聊天歷史 -->
            <div class="chat-history" x-ref="chatHistory">

                <!-- 歡迎訊息 -->
                <div class="chat-message message-ai" x-show="chatHistory.length === 0">
                    <div>歡迎來到 HSN CLI Preview！</div>
                    <div>這是一個終端式的 AI 知識探索和社交體驗。</div>
                    <div>輸入任何問題或想法，開始你的知識探索之旅...</div>
                </div>

                <!-- 聊天記錄 -->
                <template x-for="(message, index) in chatHistory" :key="index">
                    <div class="chat-message" :class="getMessageClass(message)">
                        <div x-html="formatMessage(message)"></div>
                    </div>
                </template>

                <!-- 載入指示器 -->
                <div x-show="isLoading" class="chat-message message-ai loading">
                    <div>[AI] > PROCESSING...</div>
                </div>

            </div>

            <!-- 輸入提示符 -->
            <div class="prompt-line">
                <span class="prompt-symbol">
                    <span class="mini-jellyfish-avatar" :class="getTypingColorClass()">
                        <span class="mini-sphere mini-sphere-1"></span>
                        <span class="mini-sphere mini-sphere-2"></span>
                        <span class="mini-sphere mini-sphere-3"></span>
                        <span class="mini-core"></span>
                    </span>
                    >
                    <span class="mini-cursor" x-show="!isLoading"></span>
                </span>
                <div class="input-container">
                    <textarea
                        class="prompt-input"
                        x-model="currentInput"
                        @keydown="handleKeyDown($event)"
                        @keydown.up="navigateHistory(-1)"
                        @keydown.down="navigateHistory(1)"
                        placeholder="輸入你的問題或想法... (Command+Enter 發送)"
                        :disabled="isLoading"
                        x-ref="promptInput"
                        rows="3"
                        style="resize: none; overflow: hidden; min-height: 80px;"
                    ></textarea>
                    <!-- 移動端發送按鈕 - 復古終端風格 -->
                    <button
                        class="mobile-send-button"
                        :class="{ 'loading': isLoading }"
                        @click="sendMessage()"
                        :disabled="isLoading || !currentInput.trim()"
                        x-show="currentInput.trim().length > 0"
                        x-transition:enter="transition ease-out duration-200"
                        x-transition:enter-start="opacity-0 scale-95"
                        x-transition:enter-end="opacity-100 scale-100"
                        x-transition:leave="transition ease-in duration-150"
                        x-transition:leave-start="opacity-100 scale-100"
                        x-transition:leave-end="opacity-0 scale-95"
                    >
                        <span x-show="!isLoading">[ SEND ]</span>
                        <span x-show="isLoading" style="opacity: 0;">[ .... ]</span>
                    </button>
                </div>
            </div>

            <!-- 草稿保存提示 -->
            <div x-show="draftSaved" class="draft-saved-notice">
                [*] 草稿已保存
            </div>

        </main>

        <!-- 狀態欄 - 自動淡出 -->
        <div class="status-bar"
             x-show="statusBarVisible"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0 transform translate-y-full"
             x-transition:enter-end="opacity-100 transform translate-y-0"
             x-transition:leave="transition ease-in duration-300"
             x-transition:leave-start="opacity-100 transform translate-y-0"
             x-transition:leave-end="opacity-0 transform translate-y-full"
             @click="showStatusBar()">
            <div>
                <span x-text="user ? \`USER: \${user.displayName}\` : 'GUEST'"></span>
                <span class="ml-4">|</span>
                <span class="ml-2" x-text="instantDBConnected ? '[*] SYNC' : '[x] OFFLINE'"></span>
                <!-- * Enhanced AI 狀態指示器 (HSN-47) -->
                <span class="ml-4">|</span>
                <span class="ml-2"
                      :class="{ 'text-green-400': enhancedAIEnabled, 'text-yellow-400': !enhancedAIEnabled }"
                      x-text="enhancedAIEnabled ? '[+] ENHANCED' : '[-] STANDARD'"></span>
                <span class="ml-2"
                      x-show="enhancedAIEnabled"
                      :class="{
                          'text-green-400': aiPerformanceHealth >= 80,
                          'text-yellow-400': aiPerformanceHealth >= 60 && aiPerformanceHealth < 80,
                          'text-red-400': aiPerformanceHealth < 60
                      }"
                      x-text="\`L\${currentModelTier} (\${aiPerformanceHealth}%)\`"></span>
            </div>
            <div>
                <span
                    class="clickable-title"
                    @click="toggleTitleInfo()"
                    :title="showFullTitle ? '點擊收起' : '點擊展開完整標題'"
                    x-text="showFullTitle ? '人工智慧時代的新社交媒體 | 極簡 Micro Blog + (一籃子 LLM) > Meta*10' : '極簡時代的數位生活入口 (HS-CLI Preview)'"
                ></span>
                <span class="ml-4">|</span>
                <span class="ml-2" x-text="\`訊息: \${chatHistory.length}\`"></span>
                <!-- 🌊 流式狀態指示器 -->
                <span class="ml-4" x-show="enhancedAIEnabled">|</span>
                <span class="ml-2"
                      x-show="enhancedAIEnabled"
                      :class="{ 'text-cyan-400': streamingActive, 'text-gray-400': !streamingActive }"
                      x-text="streamingActive ? '🌊 流式中' : \`🌊 \${getStreamingModeDisplayName(streamingMode)}\`"></span>
            </div>
        </div>

        <!-- 螢幕保護模式覆蓋層 -->
        <div x-show="screenSaverActive"
             x-transition:enter="transition ease-out duration-500"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-300"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             @click="deactivateScreenSaver()"
             class="screen-saver-overlay">
        </div>

    </div>

    <!-- 🔧 Alpine.js 應用邏輯 -->
    <script>
        function hsnCliApp() {
            return {
                // 狀態管理
                user: null,
                currentInput: '',
                inputDraft: '', // 保存未發送的草稿
                draftSaved: false, // 草稿保存狀態
                draftSaveTimeout: null, // 草稿提示超時
                chatHistory: [],
                inputHistory: [],
                historyIndex: -1,
                isLoading: false,
                error: null,
                instantDBConnected: false,
                instantDB: null,

                // * 狀態列自動隱藏
                statusBarVisible: true,
                statusBarTimeout: null,

                // * 右上按鈕自動隱藏
                showTopButtons: true,
                topButtonsTimer: null,

                // * 螢幕保護模式
                screenSaverActive: false,

                // 🎨 主題系統
                currentTheme: 'retro',
                themes: ['retro', 'night-owl', 'cyberpunk-purple'],
                themeNames: {
                    retro: '復古',
                    'night-owl': '冷調',
                    'cyberpunk-purple': '多彩'
                },

                // 🛡️ HSN-50: 防重複發送機制
                lastSentMessage: '',
                lastSentTime: 0,
                sendingInProgress: false, // 額外的發送狀態鎖

                // 🎨 動態頭像狀態
                avatarState: 'idle', // idle, thinking, discovering
                userColorClass: 'user-color-1',

                // 🤖 AI API 狀態
                aiProvider: 'openrouter',
                currentModel: 'openai/gpt-4o-mini',

                // 🚀 HSN-47 Enhanced AI 狀態
                enhancedAIEnabled: false,
                currentModelTier: 2, // 1=Premium, 2=Balanced, 3=Fast
                streamingEnabled: true,
                streamingMode: 'natural', // word, sentence, paragraph, natural
                typingEffect: 'retro', // classic, smooth, retro
                aiPerformanceHealth: 100,
                modelSwitchCount: 0,
                streamingActive: false,

                // 💾 聊天持久化狀態
                persistenceEnabled: true,
                autoSaveInterval: null,
                autoSaveTimeout: null,
                lastSaveTime: null,
                storageKey: 'hsn_cli_chat_history',
                maxHistorySize: 1000, // 最大保存訊息數量

                // 🎯 UI 狀態
                showFullTitle: false,
                showEnglishSubtitle: false,

                // 🌅 標題淡化系統 (HSN-44)
                headerVisible: true,
                fadeTimer: null,

                // 初始化
                async init() {
                    console.log('[>] HSN CLI Preview 初始化...');

                    // * 初始化主題系統
                    this.initTheme();

                    // 創建訪客用戶
                    this.createGuestUser();

                    // 設置個性化色彩
                    this.setupPersonalizedColors();

                    // * 恢復聊天記錄 (持久化功能)
                    this.loadChatHistory();

                    // * 初始化 Enhanced AI (HSN-47)
                    await this.initializeEnhancedAI();

                    // 初始化 InstantDB
                    await this.initializeInstantDB();

                    // * 啟動自動保存
                    this.startAutoSave();

                    // * 初始化狀態列自動隱藏
                    this.initStatusBarAutoHide();

                    // 聚焦輸入框
                    this.$nextTick(() => {
                        this.$refs.promptInput.focus();
                    });

                    // 🌅 啟動標題淡化計時器 (HSN-44)
                    this.startHeaderFadeTimer();

                    // 🧹 頁面卸載時清理計時器
                    window.addEventListener('beforeunload', () => {
                        this.cleanup();
                    });
                },

                // 創建訪客用戶
                createGuestUser() {
                    const guestId = 'guest_' + Date.now() + '_' + Math.random().toString(36).substring(2, 8);
                    const guestNames = ['探索者', '思考者', '學習者', '創新者', '發現者'];
                    const randomName = guestNames[Math.floor(Math.random() * guestNames.length)];

                    this.user = {
                        id: guestId,
                        displayName: randomName,
                        type: 'guest',
                        createdAt: Date.now()
                    };

                    console.log('👤 訪客用戶創建:', this.user.displayName);
                },

                // 🎨 設置個性化色彩
                setupPersonalizedColors() {
                    // 基於用戶 ID 生成一致的色彩
                    const colorIndex = this.hashUserId(this.user.id) % 5 + 1;
                    this.userColorClass = \`user-color-\${colorIndex}\`;
                    console.log('🎨 個性化色彩設置:', this.userColorClass);
                },

                // 生成用戶 ID 的簡單哈希
                hashUserId(userId) {
                    let hash = 0;
                    for (let i = 0; i < userId.length; i++) {
                        const char = userId.charCodeAt(i);
                        hash = ((hash << 5) - hash) + char;
                        hash = hash & hash; // 轉換為32位整數
                    }
                    return Math.abs(hash);
                },

                // 🚀 初始化 Enhanced AI (HSN-47)
                async initializeEnhancedAI() {
                    try {
                        console.log('🚀 正在初始化 Enhanced AI Integration...');

                        // 初始化 Enhanced AI API
                        if (window.aiAPI && window.aiAPI.initialize) {
                            const chatContainer = document.querySelector('.chat-history');
                            await window.aiAPI.initialize(chatContainer);
                            this.enhancedAIEnabled = true;
                            console.log('✅ Enhanced AI Integration 已啟用');

                            // 獲取初始狀態
                            this.updateAIStatus();
                        } else {
                            console.log('⚠️ Enhanced AI 不可用，使用標準模式');
                            this.enhancedAIEnabled = false;
                        }

                    } catch (error) {
                        console.error('❌ Enhanced AI 初始化失敗:', error);
                        this.enhancedAIEnabled = false;
                    }
                },

                // 📊 更新 AI 狀態
                updateAIStatus() {
                    if (this.enhancedAIEnabled && window.aiAPI.getStatusReport) {
                        try {
                            const status = window.aiAPI.getStatusReport();

                            if (status.openrouter && status.openrouter.currentModel) {
                                this.currentModelTier = status.openrouter.currentModel.level || 2;
                                // 使用 API 名稱而不是顯示名稱
                                this.currentModel = status.openrouter.currentModel.apiName || this.currentModel;
                            }

                            if (status.performance) {
                                this.aiPerformanceHealth = status.performance.health || 100;
                            }

                            if (status.streaming) {
                                this.streamingActive = status.streaming.isActive || false;
                                this.streamingMode = status.streaming.mode || 'natural';
                            }

                            console.log('📊 AI 狀態已更新:', {
                                tier: this.currentModelTier,
                                health: this.aiPerformanceHealth,
                                streaming: this.streamingActive
                            });

                        } catch (error) {
                            console.warn('⚠️ AI 狀態更新失敗:', error);
                        }
                    }
                },

                // 🎛️ 切換流式模式
                toggleStreamingMode() {
                    const modes = ['word', 'sentence', 'paragraph', 'natural'];
                    const currentIndex = modes.indexOf(this.streamingMode);
                    const nextMode = modes[(currentIndex + 1) % modes.length];

                    this.streamingMode = nextMode;

                    if (this.enhancedAIEnabled && window.aiAPI.setStreamingMode) {
                        window.aiAPI.setStreamingMode(nextMode);
                    }

                    // 添加系統訊息
                    this.chatHistory.push({
                        type: 'system',
                        content: \`🌊 流式模式已切換至「\${this.getStreamingModeDisplayName(nextMode)}\」\`,
                        timestamp: Date.now()
                    });

                    this.scrollToBottom();
                    console.log('🌊 流式模式切換:', nextMode);
                },

                // 🎨 切換打字效果
                toggleTypingEffect() {
                    const effects = ['classic', 'smooth', 'retro'];
                    const currentIndex = effects.indexOf(this.typingEffect);
                    const nextEffect = effects[(currentIndex + 1) % effects.length];

                    this.typingEffect = nextEffect;

                    if (this.enhancedAIEnabled && window.aiAPI.setTypingEffect) {
                        window.aiAPI.setTypingEffect(nextEffect);
                    }

                    // 添加系統訊息
                    this.chatHistory.push({
                        type: 'system',
                        content: \`[cfg] 打字效果已切換至「\${this.getTypingEffectDisplayName(nextEffect)}\」\`,
                        timestamp: Date.now()
                    });

                    this.scrollToBottom();
                    console.log('🎨 打字效果切換:', nextEffect);
                },

                // 📊 顯示 AI 性能報告
                showAIPerformanceReport() {
                    if (this.enhancedAIEnabled && window.aiAPI.getPerformanceAnalysis) {
                        try {
                            const analysis = window.aiAPI.getPerformanceAnalysis();

                            const reportContent = \`📊 AI 性能報告\\n\\n\` +
                                \`🤖 當前模型: \${analysis.claude?.currentModel?.name || 'Unknown'}\\n\` +
                                \`📈 健康分數: \${analysis.overall?.health || 'N/A'}%\\n\` +
                                \`🌊 流式狀態: \${analysis.streaming?.isActive ? '啟用' : '停用'}\\n\` +
                                \`⚡ 模型切換: \${this.modelSwitchCount} 次\\n\\n\` +
                                \`💡 建議: \${analysis.overall?.recommendations?.join(', ') || '無特殊建議'}\`;

                            this.chatHistory.push({
                                type: 'system',
                                content: reportContent,
                                timestamp: Date.now()
                            });

                            this.scrollToBottom();

                        } catch (error) {
                            console.error('❌ 性能報告生成失敗:', error);
                        }
                    } else {
                        this.chatHistory.push({
                            type: 'system',
                            content: '📊 Enhanced AI 未啟用，無法生成性能報告',
                            timestamp: Date.now()
                        });
                        this.scrollToBottom();
                    }
                },

                // 🏷️ 獲取流式模式顯示名稱
                getStreamingModeDisplayName(mode) {
                    const names = {
                        word: '逐詞顯示',
                        sentence: '逐句顯示',
                        paragraph: '逐段顯示',
                        natural: '自然節奏'
                    };
                    return names[mode] || mode;
                },

                // 🏷️ 獲取打字效果顯示名稱
                getTypingEffectDisplayName(effect) {
                    const names = {
                        classic: '經典',
                        smooth: '平滑',
                        retro: '復古'
                    };
                    return names[effect] || effect;
                },

                // 💾 聊天記錄持久化功能

                // 載入聊天記錄
                loadChatHistory() {
                    try {
                        if (!this.persistenceEnabled) {
                            console.log('💾 聊天持久化已停用');
                            return;
                        }

                        const savedData = localStorage.getItem(this.storageKey);
                        if (savedData) {
                            const parsedData = JSON.parse(savedData);

                            // 驗證數據結構
                            if (Array.isArray(parsedData.chatHistory)) {
                                this.chatHistory = parsedData.chatHistory;
                                this.lastSaveTime = parsedData.lastSaveTime || Date.now();

                                console.log('[*] 已恢復 ' + this.chatHistory.length + ' 條聊天記錄');

                                // 添加恢復提示訊息
                                if (this.chatHistory.length > 0) {
                                    const restoredCount = this.chatHistory.length;
                                    this.chatHistory.push({
                                        type: 'system',
                                        content: '[sys] > [*] 已恢復 ' + restoredCount + ' 條聊天記錄 (' + new Date(this.lastSaveTime).toLocaleString() + ')',
                                        timestamp: Date.now(),
                                        isRestoreMessage: true
                                    });
                                }

                                // 滾動到底部
                                this.$nextTick(() => {
                                    this.scrollToBottom();
                                });
                            }
                        } else {
                            console.log('💾 沒有找到保存的聊天記錄');
                        }
                    } catch (error) {
                        console.error('❌ 載入聊天記錄失敗:', error);
                        // 不影響正常使用，只是無法恢復歷史記錄
                    }
                },

                // 保存聊天記錄
                saveChatHistory() {
                    try {
                        if (!this.persistenceEnabled) return;

                        // 過濾掉恢復提示訊息，避免重複保存
                        const filteredHistory = this.chatHistory.filter(msg => !msg.isRestoreMessage);

                        // 限制歷史記錄大小
                        const historyToSave = filteredHistory.slice(-this.maxHistorySize);

                        const dataToSave = {
                            chatHistory: historyToSave,
                            lastSaveTime: Date.now(),
                            version: '1.0.0',
                            userAgent: navigator.userAgent.substring(0, 100) // 簡短的用戶代理信息
                        };

                        localStorage.setItem(this.storageKey, JSON.stringify(dataToSave));
                        this.lastSaveTime = Date.now();

                        console.log('[*] 已保存 ' + historyToSave.length + ' 條聊天記錄');
                    } catch (error) {
                        console.error('[!] 保存聊天記錄失敗:', error);

                        // 如果是存儲空間不足，嘗試清理舊記錄
                        if (error.name === 'QuotaExceededError') {
                            this.cleanupOldHistory();
                        }
                    }
                },

                // 清理舊記錄
                cleanupOldHistory() {
                    try {
                        console.log('🧹 存儲空間不足，清理舊記錄...');

                        // 只保留最近的一半記錄
                        const keepCount = Math.floor(this.maxHistorySize / 2);
                        this.chatHistory = this.chatHistory.slice(-keepCount);

                        // 重新嘗試保存
                        this.saveChatHistory();

                        // 添加清理提示
                        this.chatHistory.push({
                            type: 'system',
                            content: '🧹 存儲空間不足，已自動清理舊記錄，保留最近 ' + keepCount + ' 條訊息',
                            timestamp: Date.now()
                        });

                    } catch (error) {
                        console.error('❌ 清理舊記錄失敗:', error);
                    }
                },

                // 啟動自動保存
                startAutoSave() {
                    if (!this.persistenceEnabled) return;

                    // 每30秒自動保存一次
                    this.autoSaveInterval = setInterval(() => {
                        if (this.chatHistory.length > 0) {
                            this.saveChatHistory();
                        }
                    }, 30000);

                    console.log('💾 自動保存已啟動 (每30秒)');
                },

                // 停止自動保存
                stopAutoSave() {
                    if (this.autoSaveInterval) {
                        clearInterval(this.autoSaveInterval);
                        this.autoSaveInterval = null;
                        console.log('💾 自動保存已停止');
                    }
                },

                // 手動保存命令
                manualSave() {
                    this.saveChatHistory();
                    this.chatHistory.push({
                        type: 'system',
                        content: '💾 聊天記錄已手動保存 (' + new Date().toLocaleString() + ')',
                        timestamp: Date.now()
                    });
                    this.scrollToBottom();
                },

                // 清除所有聊天記錄
                clearAllHistory() {
                    try {
                        // 清除內存中的記錄
                        this.chatHistory = [];

                        // 清除本地存儲
                        localStorage.removeItem(this.storageKey);

                        console.log('🗑️ 所有聊天記錄已清除');

                        // 添加清除確認訊息
                        this.chatHistory.push({
                            type: 'system',
                            content: '🗑️ 所有聊天記錄已清除',
                            timestamp: Date.now()
                        });

                    } catch (error) {
                        console.error('❌ 清除聊天記錄失敗:', error);
                    }
                },

                // 導出聊天記錄
                exportChatHistory() {
                    try {
                        const exportData = {
                            chatHistory: this.chatHistory.filter(msg => !msg.isRestoreMessage),
                            exportTime: new Date().toISOString(),
                            version: '1.0.0',
                            source: 'HSN CLI Preview'
                        };

                        const dataStr = JSON.stringify(exportData, null, 2);
                        const dataBlob = new Blob([dataStr], { type: 'application/json' });

                        const link = document.createElement('a');
                        link.href = URL.createObjectURL(dataBlob);
                        link.download = 'hsn-chat-history-' + new Date().toISOString().split('T')[0] + '.json';
                        link.click();

                        this.chatHistory.push({
                            type: 'system',
                            content: '📤 聊天記錄已導出到下載文件夾',
                            timestamp: Date.now()
                        });

                        this.scrollToBottom();
                        console.log('📤 聊天記錄已導出');

                    } catch (error) {
                        console.error('❌ 導出聊天記錄失敗:', error);
                        this.chatHistory.push({
                            type: 'system',
                            content: '❌ 導出聊天記錄失敗',
                            timestamp: Date.now()
                        });
                    }
                },

                // 獲取存儲統計信息
                getStorageStats() {
                    try {
                        const savedData = localStorage.getItem(this.storageKey);
                        const dataSize = savedData ? new Blob([savedData]).size : 0;
                        const dataSizeKB = (dataSize / 1024).toFixed(2);

                        return {
                            messageCount: this.chatHistory.length,
                            dataSize: dataSize,
                            dataSizeKB: dataSizeKB,
                            lastSaveTime: this.lastSaveTime,
                            persistenceEnabled: this.persistenceEnabled
                        };
                    } catch (error) {
                        console.error('❌ 獲取存儲統計失敗:', error);
                        return null;
                    }
                },

                // 💾 添加聊天記錄並自動保存
                addChatMessage(message) {
                    this.chatHistory.push(message);

                    // 自動保存 (但不要太頻繁)
                    if (this.persistenceEnabled) {
                        // 延遲保存，避免頻繁寫入
                        clearTimeout(this.autoSaveTimeout);
                        this.autoSaveTimeout = setTimeout(() => {
                            this.saveChatHistory();
                        }, 2000); // 2秒後保存
                    }
                },

                // 💾 顯示存儲統計信息
                showStorageStats() {
                    const stats = this.getStorageStats();
                    if (stats) {
                        const statsContent = '💾 聊天記錄存儲統計\\n\\n' +
                            '📊 基本信息:\\n' +
                            '• 訊息數量: ' + stats.messageCount + ' 條\\n' +
                            '• 數據大小: ' + stats.dataSizeKB + ' KB\\n' +
                            '• 持久化狀態: ' + (stats.persistenceEnabled ? '✅ 已啟用' : '❌ 已停用') + '\\n\\n' +
                            '⏰ 時間信息:\\n' +
                            '• 最後保存: ' + (stats.lastSaveTime ? new Date(stats.lastSaveTime).toLocaleString() : '從未保存') + '\\n\\n' +
                            '🔧 管理命令:\\n' +
                            '• /save - 手動保存\\n' +
                            '• /export - 導出記錄\\n' +
                            '• /clear-all - 清除所有記錄';

                        this.addChatMessage({
                            type: 'system',
                            content: statsContent,
                            timestamp: Date.now()
                        });
                    } else {
                        this.addChatMessage({
                            type: 'system',
                            content: '❌ 無法獲取存儲統計信息',
                            timestamp: Date.now(),
                            error: true
                        });
                    }
                    this.scrollToBottom();
                },

                // 💾 顯示清除確認
                showClearAllConfirmation() {
                    this.addChatMessage({
                        type: 'system',
                        content: '⚠️ 確認清除所有聊天記錄？\\n\\n' +
                            '這將永久刪除所有本地保存的聊天記錄，此操作無法撤銷。\\n\\n' +
                            '如果確定要清除，請輸入: /confirm-clear-all',
                        timestamp: Date.now()
                    });
                    this.scrollToBottom();
                },

                // 💾 確認清除所有記錄
                confirmClearAll() {
                    this.clearAllHistory();
                    this.scrollToBottom();
                },

                // 🎭 頭像狀態循環 (點擊頭像時)
                cycleAvatarState() {
                    const states = ['idle', 'thinking', 'discovering'];
                    const currentIndex = states.indexOf(this.avatarState);
                    this.avatarState = states[(currentIndex + 1) % states.length];
                    console.log('🎭 頭像狀態切換:', this.avatarState);
                },

                // 🌅 水母點擊處理 (HSN-44) - 整合頭像切換與標題恢復
                onJellyfishClick() {
                    // 切換頭像狀態
                    this.cycleAvatarState();

                    // 如果標題已隱藏，則恢復顯示
                    if (!this.headerVisible) {
                        this.restoreHeader();
                        console.log('🌅 水母點擊：標題已恢復');
                    } else {
                        // 如果標題可見，重新開始計時器
                        this.startHeaderFadeTimer();
                        console.log('🌅 水母點擊：計時器已重置');
                    }
                },

                // 🎯 切換標題顯示
                toggleTitleInfo() {
                    this.showFullTitle = !this.showFullTitle;
                    console.log('🎯 標題切換:', this.showFullTitle ? '完整標題' : '簡短標題');
                },

                // � 切換副標題英文顯示
                toggleSubtitleEnglish() {
                    this.showEnglishSubtitle = !this.showEnglishSubtitle;
                    console.log('🌐 副標題切換:', this.showEnglishSubtitle ? '顯示英文' : '隱藏英文');
                },

                // 🌅 標題淡化系統方法 (HSN-44)
                startHeaderFadeTimer() {
                    // 清除現有計時器
                    if (this.fadeTimer) {
                        clearTimeout(this.fadeTimer);
                    }

                    // 設置 3 分鐘 (180秒) 計時器 - 測試模式：10秒
                    const isTestMode = window.location.search.includes('test=true');
                    const fadeDelay = isTestMode ? 10000 : 180000;
                    this.fadeTimer = setTimeout(() => {
                        this.fadeHeader();
                    }, fadeDelay);

                    const delayText = isTestMode ? '10秒 (測試模式)' : '3分鐘';
                    console.log('🌅 標題淡化計時器已啟動 (' + delayText + ')');
                },

                fadeHeader() {
                    this.headerVisible = false;
                    console.log('🌅 標題已淡化隱藏');
                },

                restoreHeader() {
                    this.headerVisible = true;
                    this.startHeaderFadeTimer(); // 重新開始計時器
                    console.log('🌅 標題已恢復顯示，重新開始計時');
                },

                // 🧹 清理計時器 (HSN-44)
                cleanup() {
                    if (this.fadeTimer) {
                        clearTimeout(this.fadeTimer);
                        this.fadeTimer = null;
                        console.log('🧹 標題淡化計時器已清理');
                    }

                    // 💾 清理持久化相關計時器
                    this.stopAutoSave();
                    if (this.autoSaveTimeout) {
                        clearTimeout(this.autoSaveTimeout);
                        this.autoSaveTimeout = null;
                    }

                    // 💾 最後保存一次
                    if (this.persistenceEnabled && this.chatHistory.length > 0) {
                        this.saveChatHistory();
                        console.log('[*] 頁面卸載前已保存聊天記錄');
                    }
                },

                // * 主題切換系統 - 整合版
                switchTheme(theme) {
                    // 支援新舊兩種主題 ID 格式
                    const themeId = this.availableThemes.find(t => t.id === theme || t.name === theme)?.id || theme;

                    if (this.availableThemes.find(t => t.id === themeId)) {
                        this.currentTheme = themeId;
                        this.applyTheme(themeId);

                        const themeName = this.availableThemes.find(t => t.id === themeId)?.name || themeId;
                        console.log('[*] 主題已切換至: ' + themeName);

                        // 添加主題切換訊息到聊天記錄 - 移除 emoji
                        this.chatHistory.push({
                            type: 'system',
                            content: '[cfg] > [*] 主題已切換至『' + themeName + '』',
                            timestamp: Date.now()
                        });
                        this.scrollToBottom();
                    }
                },

                // 🎨 主題切換系統
                switchTheme(theme) {
                    if (this.themes.includes(theme)) {
                        this.currentTheme = theme;
                        document.documentElement.setAttribute('data-theme', theme);
                        localStorage.setItem('hsn-theme', theme);
                        console.log('[*] 主題切換功能升級中');

                        // 添加主題切換的聊天訊息
                        this.chatHistory.push({
                            type: 'system',
                            content: '[cfg] > [*] 主題切換功能升級中，敬請期待',
                            timestamp: Date.now()
                        });
                        this.scrollToBottom();
                    }
                },

                // 🎨 循環切換主題
                cycleTheme() {
                    const currentIndex = this.themes.indexOf(this.currentTheme);
                    const nextIndex = (currentIndex + 1) % this.themes.length;
                    this.switchTheme(this.themes[nextIndex]);
                },

                // 🎨 初始化主題
                initTheme() {
                    const savedTheme = localStorage.getItem('hsn-theme') || 'retro';
                    this.switchTheme(savedTheme);
                },

                // * 初始化狀態列自動隱藏
                initStatusBarAutoHide() {
                    this.resetStatusBarTimer();
                    this.startTopButtonsTimer();
                },

                // * 重置狀態列計時器
                resetStatusBarTimer() {
                    if (this.statusBarTimeout) {
                        clearTimeout(this.statusBarTimeout);
                    }

                    this.statusBarVisible = true;

                    this.statusBarTimeout = setTimeout(() => {
                        this.statusBarVisible = false;
                    }, 5000); // 5秒後自動隱藏
                },


                // * 右上按鈕自動隱藏控制
                startTopButtonsTimer() {
                    if (this.topButtonsTimer) clearTimeout(this.topButtonsTimer);
                    this.topButtonsTimer = setTimeout(() => {
                        this.showTopButtons = false;
                        if (this.$refs.topButtons) {
                            this.$refs.topButtons.style.opacity = 0;
                            this.$refs.topButtons.style.pointerEvents = 'none';
                        }
                    }, 5000);
                },
                resetTopButtonsTimer() {
                    if (this.topButtonsTimer) clearTimeout(this.topButtonsTimer);
                    this.showTopButtons = true;
                    if (this.$refs.topButtons) {
                        this.$refs.topButtons.style.opacity = 1;
                        this.$refs.topButtons.style.pointerEvents = 'auto';
                    }
                },
                // * 顯示狀態列
                showStatusBar() {
                    this.statusBarVisible = true;
                    this.resetStatusBarTimer();
                },

                // * 啟動螢幕保護模式
                activateScreenSaver() {
                    this.screenSaverActive = true;
                    document.body.style.cursor = 'none';
                },

                // * 關閉螢幕保護模式
                deactivateScreenSaver() {
                    this.screenSaverActive = false;
                    document.body.style.cursor = 'default';
                },

                // �🌈 根據打字內容獲取顏色類別
                getTypingColorClass() {
                    const text = this.currentInput.toLowerCase();
                    const length = text.length;

                    if (length === 0) return 'typing-idle';
                    if (text.includes('/')) return 'typing-command';
                    if (length < 5) return 'typing-short';
                    if (length < 15) return 'typing-medium';
                    return 'typing-long';
                },

                // 初始化 InstantDB
                async initializeInstantDB() {
                    try {
                        const InstantDB = await window.loadInstantDB();
                        if (!InstantDB) {
                            throw new Error('InstantDB SDK 載入失敗');
                        }

                        this.instantDB = InstantDB.init({
                            appId: '1c81583f-e8de-4448-982e-1c616a2b3432'
                        });

                        // 設置即時查詢
                        this.instantDB.subscribeQuery({
                            chats: {},
                            users: {}
                        }, (result) => {
                            if (result.data) {
                                this.handleInstantDBData(result.data);
                                this.instantDBConnected = true;
                            }
                        });

                        console.log('✅ InstantDB 初始化成功');
                        this.instantDBConnected = true;

                    } catch (error) {
                        console.error('❌ InstantDB 初始化失敗:', error);
                        this.instantDBConnected = false;
                    }
                },

                // 處理 InstantDB 數據
                handleInstantDBData(data) {
                    // 處理聊天數據同步
                    if (data.chats) {
                        console.log('📊 聊天數據同步:', Object.keys(data.chats).length);
                    }
                },

                // ⌨️ 處理鍵盤事件
                handleKeyDown(event) {
                    // Command+Enter 或 Ctrl+Enter 發送訊息
                    if ((event.metaKey || event.ctrlKey) && event.key === 'Enter') {
                        event.preventDefault();
                        this.sendMessage();
                        return;
                    }

                    // Enter 換行
                    if (event.key === 'Enter' && !event.shiftKey && !event.metaKey && !event.ctrlKey) {
                        // 讓 textarea 自然換行，不阻止預設行為
                        this.$nextTick(() => {
                            this.autoResizeTextarea();
                        });
                        return;
                    }

                    // 其他按鍵的自動調整大小
                    this.$nextTick(() => {
                        this.autoResizeTextarea();
                    });
                },

                // 📏 自動調整 textarea 高度 - 增強版
                autoResizeTextarea() {
                    const textarea = this.$refs.promptInput;
                    if (textarea) {
                        textarea.style.height = 'auto';
                        const newHeight = Math.min(textarea.scrollHeight, 200); // 增加最大高度到 200px
                        textarea.style.height = Math.max(newHeight, 80) + 'px'; // 最小高度 80px

                        // 動態更新 CSS 變數，讓聊天容器適應輸入框高度
                        const inputAreaHeight = Math.max(newHeight, 80) + 40; // 輸入框高度 + 間距
                        document.documentElement.style.setProperty('--input-area-height', inputAreaHeight + 'px');

                        // 確保聊天內容滾動到底部
                        this.$nextTick(() => {
                            this.scrollToBottom();
                        });
                    }
                },

                // 發送訊息 (HSN-50: 修復重複發送問題)
                async sendMessage() {
                    if (!this.currentInput.trim() || this.isLoading || this.sendingInProgress) return;

                    const userMessage = this.currentInput.trim();
                    const now = Date.now();

                    // 🛡️ HSN-50: 防重複發送檢查
                    if (this.lastSentMessage === userMessage && (now - this.lastSentTime) < 2000) {
                        console.log('🛡️ 防重複發送: 相同訊息在2秒內，忽略重複發送');
                        return;
                    }

                    // 設置發送狀態鎖
                    this.sendingInProgress = true;
                    this.lastSentMessage = userMessage;
                    this.lastSentTime = now;

                    this.inputHistory.unshift(userMessage);
                    this.historyIndex = -1;

                    // 檢查是否為特殊命令
                    if (await this.handleSpecialCommands(userMessage)) {
                        this.currentInput = '';
                        return;
                    }

                    // 添加用戶訊息到歷史
                    this.addChatMessage({
                        type: 'user',
                        content: userMessage,
                        timestamp: Date.now()
                    });

                    this.currentInput = '';
                    this.isLoading = true;

                    // 滾動到底部
                    this.scrollToBottom();

                    try {
                        // 🤖 使用真實 Claude API
                        this.avatarState = 'thinking';
                        await this.callRealAI(userMessage);
                    } catch (error) {
                        console.error('❌ AI 回應錯誤:', error);
                        this.avatarState = 'idle';
                        // HSN-50 修復: 移除重複的錯誤訊息添加
                        // callRealAI 中的 fallbackToSimulation 已經會處理錯誤回應
                        // 不需要在這裡再次添加錯誤訊息
                    } finally {
                        this.isLoading = false;
                        this.sendingInProgress = false; // 🛡️ HSN-50: 釋放發送狀態鎖
                        this.avatarState = 'idle';
                        this.scrollToBottom();
                    }
                },

                // 🔍 處理特殊命令
                async handleSpecialCommands(input) {
                    const command = input.toLowerCase();

                    // 幫助命令
                    if (command === '/help' || command === '/h') {
                        this.showHelpMessage();
                        return true;
                    }

                    // 知識搜索命令
                    if (command.startsWith('/search ') || command.startsWith('/s ')) {
                        const query = input.substring(input.indexOf(' ') + 1).trim();
                        if (query) {
                            await this.performKnowledgeSearch(query);
                        } else {
                            this.showCommandError('搜索命令需要查詢內容。用法: /search <查詢內容>');
                        }
                        return true;
                    }

                    // 知識命令
                    if (command.startsWith('/knowledge ') || command.startsWith('/k ')) {
                        const query = input.substring(input.indexOf(' ') + 1).trim();
                        if (query) {
                            await this.performKnowledgeSearch(query);
                        } else {
                            this.showCommandError('知識命令需要查詢內容。用法: /knowledge <查詢內容>');
                        }
                        return true;
                    }

                    // 清除命令
                    if (command === '/clear' || command === '/c') {
                        this.clearChat();
                        return true;
                    }

                    // 狀態命令
                    if (command === '/status' || command === '/st') {
                        this.showStatus();
                        return true;
                    }

                    // 知識庫命令
                    if (command === '/knowledge-list' || command === '/kl') {
                        this.showKnowledgeList();
                        return true;
                    }

                    // 🎨 主題切換命令
                    if (command.startsWith('/theme ') || command.startsWith('/t ')) {
                        const theme = input.substring(input.indexOf(' ') + 1).trim().toLowerCase();
                        if (this.themes.includes(theme)) {
                            this.switchTheme(theme);
                        } else {
                            this.showCommandError('無效的主題。可用主題: ' + this.themes.join(', '));
                        }
                        return true;
                    }

                    // 主題列表命令
                    if (command === '/themes' || command === '/tl') {
                        this.showThemeList();
                        return true;
                    }

                    // 🚀 Enhanced AI 命令 (HSN-47)

                    // AI 性能報告命令
                    if (command === '/ai-status' || command === '/as') {
                        this.showAIPerformanceReport();
                        return true;
                    }

                    // 流式模式切換命令
                    if (command === '/streaming' || command === '/sm') {
                        this.toggleStreamingMode();
                        return true;
                    }

                    // 打字效果切換命令
                    if (command === '/typing' || command === '/te') {
                        this.toggleTypingEffect();
                        return true;
                    }

                    // Enhanced AI 詳細幫助命令
                    if (command === '/ai-help' || command === '/ah') {
                        this.showEnhancedAIHelp();
                        return true;
                    }

                    // 💾 聊天持久化命令

                    // 手動保存命令
                    if (command === '/save' || command === '/sv') {
                        this.manualSave();
                        return true;
                    }

                    // 導出聊天記錄命令
                    if (command === '/export' || command === '/ex') {
                        this.exportChatHistory();
                        return true;
                    }

                    // 存儲統計命令
                    if (command === '/storage' || command === '/st') {
                        this.showStorageStats();
                        return true;
                    }

                    // 清除所有記錄命令 (需要確認)
                    if (command === '/clear-all' || command === '/ca') {
                        this.showClearAllConfirmation();
                        return true;
                    }

                    // 確認清除所有記錄命令
                    if (command === '/confirm-clear-all') {
                        this.confirmClearAll();
                        return true;
                    }

                    return false;
                },

                // 顯示幫助訊息
                showHelpMessage() {
                    const helpContent = '🖥️ HSN CLI Preview 命令幫助\\n\\n' +
                        '📋 基本命令:\\n' +
                        '• /help, /h - 顯示此幫助訊息\\n' +
                        '• /search <查詢>, /s <查詢> - 使用 Tavily 搜索知識\\n' +
                        '• /knowledge <查詢>, /k <查詢> - 知識增強搜索\\n' +
                        '• /knowledge-list, /kl - 查看已保存的知識節點\\n' +
                        '• /clear, /c - 清除聊天記錄\\n' +
                        '• /status, /st - 顯示系統狀態\\n\\n' +
                        '[*] 主題系統:\\n' +
                        '• /theme <主題>, /t <主題> - 切換主題 (retro/night-owl/cyberpunk-purple)\\n' +
                        '• /themes, /tl - 查看可用主題\\n\\n' +
                        '🚀 Enhanced AI (HSN-47):\\n' +
                        '• /ai-status, /as - 顯示 AI 性能報告\\n' +
                        '• /streaming, /sm - 切換流式顯示模式\\n' +
                        '• /typing, /te - 切換打字效果\\n' +
                        '• /ai-help, /ah - Enhanced AI 詳細說明\\n\\n' +
                        '💾 聊天持久化:\\n' +
                        '• /save, /sv - 手動保存聊天記錄\\n' +
                        '• /export, /ex - 導出聊天記錄\\n' +
                        '• /storage, /st - 查看存儲統計\\n' +
                        '• /clear-all, /ca - 清除所有記錄\\n\\n' +
                        '📝 範例:\\n' +
                        '• /search 人工智慧的發展歷史\\n' +
                        '• /knowledge 量子計算原理\\n' +
                        '• /theme cool\\n' +
                        '• /ai-status\\n' +
                        '• /streaming\\n\\n' +
                        '💬 直接輸入文字即可與 AI 對話。';

                    this.chatHistory.push({
                        type: 'system',
                        content: helpContent,
                        timestamp: Date.now()
                    });
                    this.scrollToBottom();
                },

                // 🚀 顯示 Enhanced AI 詳細幫助
                showEnhancedAIHelp() {
                    const helpContent = '🚀 Enhanced AI (HSN-47) 詳細說明\\n\\n' +
                        '🤖 多模型智能管理:\\n' +
                        '• 自動根據對話複雜度選擇最適合的 Claude 模型\\n' +
                        '• Level 1: Claude 3.5 Sonnet (最高品質)\\n' +
                        '• Level 2: Claude 3 Sonnet (平衡選擇)\\n' +
                        '• Level 3: Claude 3 Haiku (高速回應)\\n\\n' +
                        '⚡ 智能升降級:\\n' +
                        '• 529 錯誤自動降級重試\\n' +
                        '• 連續失敗自動降級\\n' +
                        '• 性能優秀自動升級\\n' +
                        '• 成本效益平衡算法\\n\\n' +
                        '🌊 流式回應模式:\\n' +
                        '• 逐詞顯示 - 適合短回應\\n' +
                        '• 逐句顯示 - 適合對話\\n' +
                        '• 逐段顯示 - 適合長文\\n' +
                        '• 自然節奏 - 智能適應\\n\\n' +
                        '🎨 打字效果:\\n' +
                        '• 經典 - 傳統閃爍游標\\n' +
                        '• 平滑 - 淡入效果\\n' +
                        '• 復古 - 終端風格 + 音效\\n\\n' +
                        '📊 性能監控:\\n' +
                        '• 即時健康分數追蹤\\n' +
                        '• 模型切換統計\\n' +
                        '• 回應時間分析\\n' +
                        '• 用戶體驗優化建議\\n\\n' +
                        '🎯 當前狀態:\\n' +
                        \`• Enhanced AI: \${this.enhancedAIEnabled ? '✅ 已啟用' : '❌ 未啟用'}\\n\` +
                        \`• 模型層級: Level \${this.currentModelTier}\\n\` +
                        \`• 流式模式: \${this.getStreamingModeDisplayName(this.streamingMode)}\\n\` +
                        \`• 打字效果: \${this.getTypingEffectDisplayName(this.typingEffect)}\\n\` +
                        \`• 健康分數: \${this.aiPerformanceHealth}%\`;

                    this.chatHistory.push({
                        type: 'system',
                        content: helpContent,
                        timestamp: Date.now()
                    });
                    this.scrollToBottom();
                },

                // 顯示命令錯誤
                showCommandError(message) {
                    this.chatHistory.push({
                        type: 'system',
                        content: '❌ ' + message,
                        timestamp: Date.now(),
                        error: true
                    });
                    this.scrollToBottom();
                },

                // 🎨 顯示主題列表
                showThemeList() {
                    const themeContent = '[*] 可用主題:\\n\\n' +
                        '• retro (復古) - 經典綠色終端風格\\n' +
                        '• night-owl (冷調) - 藍色科技風格\\n' +
                        '• cyberpunk-purple (多彩) - 彩虹霓虹風格\\n\\n' +
                        '當前主題: ' + this.themeNames[this.currentTheme] + '\\n\\n' +
                        '使用方法: /theme <主題名稱>\\n' +
                        '範例: /theme night-owl';

                    this.chatHistory.push({
                        type: 'system',
                        content: themeContent,
                        timestamp: Date.now()
                    });
                    this.scrollToBottom();
                },

                // 清除聊天
                clearChat() {
                    this.chatHistory = [];
                    this.chatHistory.push({
                        type: 'system',
                        content: '✅ 聊天記錄已清除',
                        timestamp: Date.now()
                    });
                    this.scrollToBottom();
                },

                // 顯示狀態
                showStatus() {
                    let statusContent = '📊 系統狀態\\n\\n' +
                        '👤 用戶: ' + (this.user ? this.user.displayName : '未知') + '\\n' +
                        '🌐 連接狀態: ' + (this.instantDBConnected ? '🟢 已連接' : '🔴 離線') + '\\n' +
                        '💬 聊天記錄: ' + this.chatHistory.length + ' 條\\n' +
                        '🎭 頭像狀態: ' + this.avatarState + '\\n' +
                        '🎨 當前主題: ' + this.themeNames[this.currentTheme] + '\\n\\n';

                    // 🚀 Enhanced AI 狀態 (HSN-47)
                    if (this.enhancedAIEnabled) {
                        statusContent += '🚀 Enhanced AI 狀態:\\n' +
                            '• 模型層級: Level ' + this.currentModelTier + '\\n' +
                            '• 當前模型: ' + this.currentModel + '\\n' +
                            '• 健康分數: ' + this.aiPerformanceHealth + '%\\n' +
                            '• 模型切換: ' + this.modelSwitchCount + ' 次\\n' +
                            '• 流式模式: ' + this.getStreamingModeDisplayName(this.streamingMode) + '\\n' +
                            '• 打字效果: ' + this.getTypingEffectDisplayName(this.typingEffect) + '\\n' +
                            '• 流式狀態: ' + (this.streamingActive ? '🌊 進行中' : '⏸️ 待機') + '\\n\\n';
                    } else {
                        statusContent += '🤖 標準 AI 模式:\\n' +
                            '• 當前模型: ' + this.currentModel + '\\n' +
                            '• Enhanced AI: ❌ 未啟用\\n\\n';
                    }

                    statusContent += '📦 版本資訊:\\n' +
                        '• HSN CLI Preview v0.2.0\\n' +
                        '• HSN-47 Enhanced AI Integration\\n' +
                        '• 多模型智能管理 + 流式回應系統';

                    this.chatHistory.push({
                        type: 'system',
                        content: statusContent,
                        timestamp: Date.now()
                    });
                    this.scrollToBottom();
                },

                // 📚 顯示知識列表
                async showKnowledgeList() {
                    if (!this.instantDBConnected || !this.instantDB) {
                        this.chatHistory.push({
                            type: 'system',
                            content: '❌ 知識庫功能需要 InstantDB 連接。請檢查網絡連接。',
                            timestamp: Date.now(),
                            error: true
                        });
                        this.scrollToBottom();
                        return;
                    }

                    try {
                        // 使用 InstantDB 的訂閱查詢來獲取知識搜索記錄
                        const queryResult = await new Promise((resolve, reject) => {
                            let unsubscribe;

                            unsubscribe = this.instantDB.subscribeQuery({
                                knowledgeSearches: {
                                    $: {
                                        where: {
                                            userId: this.user.id
                                        }
                                    }
                                }
                            }, (result) => {
                                if (unsubscribe) unsubscribe();
                                if (result.error) {
                                    reject(result.error);
                                } else {
                                    resolve(result);
                                }
                            });

                            // 設置超時
                            setTimeout(() => {
                                if (unsubscribe) unsubscribe();
                                reject(new Error('查詢超時'));
                            }, 5000);
                        });

                        const searches = queryResult.data?.knowledgeSearches || [];

                        if (searches.length === 0) {
                            this.chatHistory.push({
                                type: 'system',
                                content: '📚 知識庫為空\\n\\n還沒有保存任何知識搜索記錄。\\n使用 /search <查詢> 來搜索並自動保存知識。',
                                timestamp: Date.now()
                            });
                        } else {
                            let content = '📚 已保存的知識節點 (' + searches.length + ' 個)\\n\\n';

                            searches.forEach((search, index) => {
                                const date = new Date(search.timestamp).toLocaleDateString();
                                const time = new Date(search.timestamp).toLocaleTimeString();
                                content += (index + 1) + '. ' + search.query + '\\n';
                                content += '   📅 ' + date + ' ' + time + '\\n';
                                content += '   📊 ' + (search.totalResults || 0) + ' 個結果\\n';
                                if (search.answer) {
                                    content += '   💡 ' + search.answer.substring(0, 80) + '...\\n';
                                }
                                content += '\\n';
                            });

                            content += '💡 提示: 使用 /search <查詢> 來添加新的知識節點';

                            this.chatHistory.push({
                                type: 'knowledge',
                                content: content,
                                timestamp: Date.now()
                            });
                        }
                    } catch (error) {
                        console.error('❌ 知識列表查詢失敗:', error);
                        this.chatHistory.push({
                            type: 'system',
                            content: '❌ 知識列表查詢失敗: ' + error.message,
                            timestamp: Date.now(),
                            error: true
                        });
                    }

                    this.scrollToBottom();
                },

                // 🔍 執行知識搜索
                async performKnowledgeSearch(query) {
                    this.chatHistory.push({
                        type: 'user',
                        content: '/search ' + query,
                        timestamp: Date.now()
                    });

                    this.isLoading = true;
                    this.avatarState = 'discovering';
                    this.scrollToBottom();

                    try {
                        console.log('🔍 開始 Tavily 知識搜索:', query);

                        const response = await fetch('/api/tavily/search', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                query: query,
                                maxResults: 3,
                                searchDepth: 'basic'
                            })
                        });

                        if (!response.ok) {
                            throw new Error('搜索失敗: ' + response.status);
                        }

                        const data = await response.json();

                        if (data.success) {
                            this.displayKnowledgeResults(query, data);
                            console.log('✅ Tavily 搜索成功:', data.results?.length || 0, '個結果');
                        } else {
                            throw new Error(data.error || '搜索失敗');
                        }

                    } catch (error) {
                        console.error('❌ 知識搜索錯誤:', error);
                        this.chatHistory.push({
                            type: 'system',
                            content: '❌ 知識搜索失敗: ' + error.message,
                            timestamp: Date.now(),
                            error: true
                        });
                    } finally {
                        this.isLoading = false;
                        this.avatarState = 'idle';
                        this.scrollToBottom();
                    }
                },

                // 🎯 顯示知識搜索結果
                displayKnowledgeResults(query, data) {
                    let content = this.formatKnowledgeHeader(query, data);

                    if (data.answer) {
                        content += this.formatKnowledgeAnswer(data.answer);
                    }

                    if (data.results && data.results.length > 0) {
                        content += this.formatKnowledgeResults(data.results);
                    } else {
                        content += '┌─ 搜索結果 ─────────────────────────────────┐\\n';
                        content += '│ ❌ 未找到相關結果                          │\\n';
                        content += '└───────────────────────────────────────────┘\\n\\n';
                    }

                    content += this.formatKnowledgeFooter(data);

                    this.chatHistory.push({
                        type: 'knowledge',
                        content: content,
                        timestamp: Date.now(),
                        searchData: data
                    });

                    // 如果 InstantDB 已連接，保存搜索記錄
                    if (this.instantDBConnected && this.instantDB) {
                        this.saveKnowledgeSearch(query, data);
                    }
                },

                // 🎨 格式化知識搜索標題
                formatKnowledgeHeader(query, data) {
                    const border = '═'.repeat(50);
                    let header = '╔' + border + '╗\\n';
                    header += '║ 🔍 TAVILY 知識搜索引擎                        ║\\n';
                    header += '╠' + border + '╣\\n';
                    header += '║ 查詢: ' + this.padRight(query, 42) + ' ║\\n';
                    header += '║ 結果: ' + this.padRight(data.totalResults + ' 個匹配項目', 42) + ' ║\\n';
                    header += '╚' + border + '╝\\n\\n';
                    return header;
                },

                // 🎨 格式化智能摘要
                formatKnowledgeAnswer(answer) {
                    let content = '┌─ 💡 AI 智能摘要 ──────────────────────────────┐\\n';
                    const lines = this.wrapText(answer, 45);
                    lines.forEach(line => {
                        content += '│ ' + this.padRight(line, 45) + ' │\\n';
                    });
                    content += '└───────────────────────────────────────────────┘\\n\\n';
                    return content;
                },

                // 🎨 格式化搜索結果
                formatKnowledgeResults(results) {
                    let content = '┌─ 📚 搜索結果 (' + results.length + ' 項) ──────────────────────────┐\\n';

                    results.forEach((result, index) => {
                        content += '├─ [' + (index + 1) + '] ' + this.truncateText(result.title, 40) + '\\n';

                        const contentLines = this.wrapText(result.content, 43);
                        contentLines.slice(0, 2).forEach(line => {
                            content += '│   ' + this.padRight(line, 43) + ' │\\n';
                        });

                        content += '│   🔗 ' + this.truncateText(result.url, 41) + ' │\\n';
                        content += '│   📊 相關度: ' + this.padRight((result.score * 100).toFixed(1) + '%', 33) + ' │\\n';

                        if (index < results.length - 1) {
                            content += '├' + '─'.repeat(47) + '┤\\n';
                        }
                    });

                    content += '└───────────────────────────────────────────────┘\\n\\n';
                    return content;
                },

                // 🎨 格式化頁腳
                formatKnowledgeFooter(data) {
                    const time = new Date(data.searchTime).toLocaleTimeString();
                    let footer = '┌─ ℹ️  搜索信息 ─────────────────────────────────┐\\n';
                    footer += '│ ⏰ 搜索時間: ' + this.padRight(time, 32) + ' │\\n';
                    footer += '│ 🔍 數據來源: ' + this.padRight('Tavily AI Search', 32) + ' │\\n';
                    footer += '│ 🎯 查詢模式: ' + this.padRight('基礎搜索', 32) + ' │\\n';
                    footer += '└───────────────────────────────────────────────┘';
                    return footer;
                },

                // 🛠️ 文字處理工具方法
                padRight(text, length) {
                    return text.length >= length ? text.substring(0, length) : text + ' '.repeat(length - text.length);
                },

                truncateText(text, maxLength) {
                    return text.length > maxLength ? text.substring(0, maxLength - 3) + '...' : text;
                },

                wrapText(text, maxWidth) {
                    const words = text.split(' ');
                    const lines = [];
                    let currentLine = '';

                    words.forEach(word => {
                        if ((currentLine + word).length <= maxWidth) {
                            currentLine += (currentLine ? ' ' : '') + word;
                        } else {
                            if (currentLine) lines.push(currentLine);
                            currentLine = word;
                        }
                    });

                    if (currentLine) lines.push(currentLine);
                    return lines;
                },

                // 💾 保存知識搜索到 InstantDB
                async saveKnowledgeSearch(query, searchData) {
                    try {
                        const InstantDB = await window.loadInstantDB();
                        const searchId = InstantDB.id();

                        // 準備要保存的數據
                        const knowledgeRecord = {
                            userId: this.user.id,
                            userDisplayName: this.user.displayName,
                            query: query,
                            answer: searchData.answer || null,
                            totalResults: searchData.totalResults || 0,
                            searchTime: searchData.searchTime,
                            timestamp: Date.now(),
                            source: 'tavily',
                            // 保存結果摘要而不是完整結果（避免數據過大）
                            resultsSummary: (searchData.results || []).map(result => ({
                                title: result.title,
                                url: result.url,
                                score: result.score,
                                contentPreview: result.content ? result.content.substring(0, 200) : ''
                            }))
                        };

                        await this.instantDB.transact(
                            this.instantDB.tx.knowledgeSearches[searchId].update(knowledgeRecord)
                        );

                        console.log('✅ 知識搜索記錄已保存到 InstantDB:', searchId);

                        // 顯示保存成功的提示
                        this.chatHistory.push({
                            type: 'system',
                            content: '[sys] > [*] 知識已保存到雲端知識庫 (ID: ' + searchId.substring(0, 8) + '...)',
                            timestamp: Date.now()
                        });
                        this.scrollToBottom();

                    } catch (error) {
                        console.error('[!] 知識搜索記錄保存失敗:', error);

                        // 顯示保存失敗的提示
                        this.chatHistory.push({
                            type: 'system',
                            content: '[sys] > [!] 知識保存失敗: ' + error.message,
                            timestamp: Date.now(),
                            error: true
                        });
                        this.scrollToBottom();
                    }
                },

                // 🤖 真實 AI API 調用 (Enhanced with HSN-47)
                async callRealAI(userMessage) {
                    try {
                        console.log('🤖 調用 Enhanced AI API:', userMessage.substring(0, 50) + '...');

                        // 🧪 臨時模擬模式檢查 (用於測試 HSN-44 功能)
                        if (window.location.search.includes('demo=true')) {
                            console.log('🎭 使用模擬模式回應');
                            await this.fallbackToSimulation(userMessage);
                            return;
                        }

                        // 準備對話歷史
                        const conversationHistory = this.chatHistory
                            .filter(msg => !msg.error)
                            .slice(-10) // 最近10條訊息
                            .map(msg => ({
                                role: msg.type === 'user' ? 'user' : 'assistant',
                                content: msg.content
                            }));

                        // 🚀 使用 Enhanced AI API (HSN-47)
                        const result = await window.aiAPI.callAI(userMessage, {
                            model: this.currentModel,
                            conversationHistory: conversationHistory,
                            maxTokens: this.enhancedAIEnabled ? 300 : 150, // Enhanced AI 允許更長回應
                            systemPrompt: this.enhancedAIEnabled ?
                                '你是 HSN 的 AI 夥伴。請用友善、專業的方式回應，適當使用復古 ASCII 表情符號如 =__= (無奈) ^o^ (開心) XD (大笑) Orz (崩潰) Q_Q (哭泣) @_@ (暈眩) >_< (困擾) T_T (難過) 以及終端符號如 * + - = | 等。偶爾可以使用注音文如「ㄅ錯」「ㄏㄏ」「ㄎㄎ」增加親切感。根據問題複雜度調整回應長度。重要：完全避免使用現代表情符號（如 😊 🎉 👏 💪 等）。' :
                                '請用簡潔、友善的方式回應，控制在100字以內。使用復古 ASCII 表情符號如 =__= ^o^ XD Orz Q_Q @_@ >_< T_T 和終端符號如 * + - = | 等。偶爾使用注音文如「ㄅ錯」「ㄏㄏ」。避免現代表情符號，保持復古終端風格。',
                            priority: conversationHistory.length > 5 ? 'quality' : 'speed' // 長對話優先品質
                        });

                        if (result.success) {
                            this.avatarState = 'discovering';

                            // 🚀 Enhanced AI 狀態更新 (HSN-47)
                            if (this.enhancedAIEnabled) {
                                // 檢查模型是否切換
                                if (result.modelTier && result.modelTier !== this.currentModelTier) {
                                    this.currentModelTier = result.modelTier;
                                    this.modelSwitchCount++;
                                    console.log('🔄 模型層級切換至 Level', result.modelTier);
                                }

                                // 更新性能指標
                                if (result.responseTime) {
                                    // 基於回應時間更新健康分數
                                    const responseTime = result.responseTime;
                                    if (responseTime < 3000) {
                                        this.aiPerformanceHealth = Math.min(100, this.aiPerformanceHealth + 1);
                                    } else if (responseTime > 10000) {
                                        this.aiPerformanceHealth = Math.max(60, this.aiPerformanceHealth - 5);
                                    }
                                }

                                // 更新流式狀態
                                this.streamingActive = result.streamingUsed || false;

                                // 更新 AI 狀態
                                this.updateAIStatus();
                            }

                            const aiMessage = {
                                type: 'ai',
                                content: result.response,
                                timestamp: result.timestamp,
                                provider: result.provider,
                                model: result.model,
                                // 🚀 Enhanced AI 額外資訊
                                modelTier: result.modelTier,
                                responseTime: result.responseTime,
                                wasRetried: result.wasRetried,
                                streamingUsed: result.streamingUsed
                            };

                            this.addChatMessage(aiMessage);

                            // 如果 InstantDB 已連接，保存聊天記錄
                            if (this.instantDBConnected && this.instantDB) {
                                try {
                                    const InstantDB = await window.loadInstantDB();
                                    const chatId = InstantDB.id();

                                    await this.instantDB.transact(
                                        this.instantDB.tx.chats[chatId].update({
                                            userId: this.user.id,
                                            userMessage: userMessage,
                                            aiResponse: result.response,
                                            provider: result.provider,
                                            model: result.model,
                                            modelTier: result.modelTier,
                                            responseTime: result.responseTime,
                                            timestamp: Date.now()
                                        })
                                    );
                                } catch (error) {
                                    console.error('[!] 聊天記錄保存失敗:', error);
                                }
                            }

                            console.log('✅ Enhanced AI 回應成功:', {
                                provider: result.provider,
                                model: result.model,
                                tier: result.modelTier,
                                responseTime: result.responseTime,
                                streaming: result.streamingUsed,
                                retried: result.wasRetried
                            });
                        } else {
                            throw new Error('AI API 回應失敗');
                        }

                    } catch (error) {
                        console.error('❌ AI API 調用失敗:', error);

                        // 降級到模擬回應
                        await this.fallbackToSimulation(userMessage);
                    }
                },

                // 🔄 降級到模擬回應 (HSN-50: 修復重複發送問題)
                async fallbackToSimulation(userMessage) {
                    console.log('🔄 降級到模擬 AI 回應');

                    const responses = [
                        \`很有趣的問題！關於「\${userMessage}」，我認為這涉及到多個層面的思考...\`,
                        \`讓我來分析一下「\${userMessage}」這個話題。首先，我們可以從不同角度來看待這個問題...\`,
                        \`「\${userMessage}」是一個值得深入探討的主題。根據我的理解，這可能涉及到...\`,
                        \`關於「\${userMessage}」，我想分享一些見解。這個話題讓我想到了...\`
                    ];

                    // 模擬網絡延遲
                    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 1000));

                    const randomResponse = responses[Math.floor(Math.random() * responses.length)];

                    // HSN-50 修復: 使用 addChatMessage 而不是直接 push，確保一致性
                    this.addChatMessage({
                        type: 'ai',
                        content: randomResponse + ' (模擬回應)',
                        timestamp: Date.now(),
                        fallback: true
                    });
                },

                // 歷史導航
                navigateHistory(direction) {
                    if (this.inputHistory.length === 0) return;

                    // 保存當前輸入作為草稿
                    if (this.historyIndex === -1 && this.currentInput.trim()) {
                        this.inputDraft = this.currentInput;
                        this.showDraftSavedMessage();
                    }

                    if (direction === -1) { // 上箭頭
                        this.historyIndex = Math.min(this.historyIndex + 1, this.inputHistory.length - 1);
                    } else if (direction === 1) { // 下箭頭
                        this.historyIndex = Math.max(this.historyIndex - 1, -1);
                    }

                    if (this.historyIndex >= 0) {
                        this.currentInput = this.inputHistory[this.historyIndex];
                    } else {
                        // 恢復草稿或清空
                        this.currentInput = this.inputDraft || '';
                    }
                },

                // 💾 顯示草稿保存提示
                showDraftSavedMessage() {
                    // 避免重複提示
                    if (this.draftSaveTimeout) {
                        clearTimeout(this.draftSaveTimeout);
                    }

                    // 顯示臨時提示
                    this.draftSaved = true;

                    // 3秒後隱藏提示
                    this.draftSaveTimeout = setTimeout(() => {
                        this.draftSaved = false;
                    }, 3000);
                },

                // 獲取訊息樣式類別
                getMessageClass(message) {
                    switch (message.type) {
                        case 'user': return 'message-user';
                        case 'ai': return 'message-ai';
                        case 'system': return 'message-system';
                        case 'knowledge': return 'message-knowledge';
                        default: return 'message-ai';
                    }
                },

                // 格式化訊息
                formatMessage(message) {
                    const timestamp = new Date(message.timestamp).toLocaleTimeString();
                    let prefix;

                    switch (message.type) {
                        case 'user': prefix = '<span class="user-jellyfish-prefix">�</span> >'; break;
                        case 'ai': prefix = '<span class="ai-jellyfish-prefix">🌊</span> >'; break;
                        case 'system': prefix = '[sys] >'; break;
                        case 'knowledge': prefix = '[?] >'; break;
                        default: prefix = '[!] >';
                    }

                    const content = message.content;

                    return '<span style="opacity: 0.6;">' + timestamp + '</span> ' + prefix + ' ' + content;
                },

                // 滾動到底部
                scrollToBottom() {
                    this.$nextTick(() => {
                        const chatHistory = this.$refs.chatHistory;
                        if (chatHistory) {
                            chatHistory.scrollTop = chatHistory.scrollHeight;
                        }
                    });
                }
            }
        }
    </script>
</body>
</html>`);
});

export default app;
