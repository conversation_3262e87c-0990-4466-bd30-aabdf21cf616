import { Hono } from 'hono';

const app = new Hono();

// 🎭 水母版本預覽引擎 - 精密內部零件運轉感
app.get('/jellyfish-preview', (c) => {
    return c.html(`<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水母版本預覽引擎 | 精密內部零件運轉感</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        :root {
            --color-primary: #00ff41;
            --color-secondary: #0066cc;
            --color-accent: #ff6b35;
            --color-neon-cyan: #00ffff;
            --color-neon-magenta: #ff00ff;
            --color-neon-yellow: #ffff00;
            --bg-primary: #0a0a0a;
            --bg-secondary: #1a1a1a;
        }

        body {
            background: var(--bg-primary);
            color: var(--color-primary);
            font-family: 'Courier New', monospace;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .preview-container {
            display: flex;
            gap: 60px;
            align-items: center;
            justify-content: center;
            width: 100%;
            max-width: 1200px;
            padding: 40px;
        }

        .jellyfish-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }

        .section-title {
            font-size: 18px;
            color: var(--color-neon-cyan);
            text-align: center;
            margin-bottom: 10px;
            text-shadow: 0 0 10px var(--color-neon-cyan);
        }

        /* 🐙 舊版 3D 水母動畫 */
        .jellyfish-3d {
            width: 120px;
            height: 120px;
            position: relative;
            cursor: pointer;
            perspective: 300px;
            transform-style: preserve-3d;
        }

        .jellyfish-sphere-3d {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            transform-style: preserve-3d;
        }

        .sphere-layer-3d {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            border: 1px solid transparent;
            background: radial-gradient(
                circle at 30% 30%,
                rgba(255, 0, 128, 0.3) 0%,
                rgba(0, 255, 255, 0.2) 25%,
                rgba(255, 255, 0, 0.2) 50%,
                rgba(255, 128, 0, 0.2) 75%,
                transparent 100%
            );
            animation: sphere-wave-3d 8s ease-in-out infinite;
        }

        .layer-1-3d {
            transform: translateZ(0px) scale(1);
            background: radial-gradient(
                circle at 30% 30%,
                rgba(255, 0, 128, 0.4) 0%,
                rgba(128, 0, 255, 0.3) 50%,
                transparent 100%
            );
            animation-delay: 0s;
        }

        .layer-2-3d {
            transform: translateZ(-10px) scale(0.9);
            background: radial-gradient(
                circle at 40% 40%,
                rgba(0, 255, 255, 0.3) 0%,
                rgba(0, 128, 255, 0.2) 50%,
                transparent 100%
            );
            animation-delay: 2s;
        }

        .layer-3-3d {
            transform: translateZ(-20px) scale(0.8);
            background: radial-gradient(
                circle at 50% 50%,
                rgba(255, 255, 0, 0.3) 0%,
                rgba(255, 128, 0, 0.2) 50%,
                transparent 100%
            );
            animation-delay: 4s;
        }

        .layer-4-3d {
            transform: translateZ(-30px) scale(0.7);
            background: radial-gradient(
                circle at 60% 60%,
                rgba(255, 128, 0, 0.3) 0%,
                rgba(255, 0, 128, 0.2) 50%,
                transparent 100%
            );
            animation-delay: 6s;
        }

        /* 🔧 新版精密內部零件運轉感 */
        .jellyfish-mechanical {
            width: 120px;
            height: 120px;
            position: relative;
            cursor: pointer;
            perspective: 300px;
            transform-style: preserve-3d;
        }

        .mechanical-core {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 80px;
            height: 80px;
            transform: translate(-50%, -50%);
            border-radius: 50%;
            background: radial-gradient(
                circle at center,
                rgba(0, 255, 65, 0.8) 0%,
                rgba(0, 255, 65, 0.4) 30%,
                rgba(0, 255, 65, 0.1) 70%,
                transparent 100%
            );
            border: 2px solid var(--color-primary);
            animation: core-pulse 3s ease-in-out infinite;
        }

        .gear-system {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .gear {
            position: absolute;
            border-radius: 50%;
            border: 2px solid var(--color-neon-cyan);
            background: radial-gradient(
                circle at center,
                rgba(0, 255, 255, 0.2) 0%,
                rgba(0, 255, 255, 0.1) 50%,
                transparent 100%
            );
        }

        .gear-1 {
            width: 40px;
            height: 40px;
            top: -20px;
            left: -20px;
            animation: gear-rotate-cw 4s linear infinite;
        }

        .gear-2 {
            width: 30px;
            height: 30px;
            top: -15px;
            right: -35px;
            animation: gear-rotate-ccw 3s linear infinite;
        }

        .gear-3 {
            width: 25px;
            height: 25px;
            bottom: -25px;
            left: -12px;
            animation: gear-rotate-cw 5s linear infinite;
        }

        .gear-4 {
            width: 35px;
            height: 35px;
            bottom: -18px;
            right: -30px;
            animation: gear-rotate-ccw 2.5s linear infinite;
        }

        /* 數據流動畫 */
        .data-stream {
            position: absolute;
            width: 2px;
            height: 20px;
            background: linear-gradient(
                to bottom,
                var(--color-neon-yellow),
                transparent
            );
            animation: data-flow 2s ease-in-out infinite;
        }

        .stream-1 {
            top: 10px;
            left: 30px;
            animation-delay: 0s;
        }

        .stream-2 {
            top: 20px;
            right: 25px;
            animation-delay: 0.5s;
        }

        .stream-3 {
            bottom: 15px;
            left: 40px;
            animation-delay: 1s;
        }

        .stream-4 {
            bottom: 25px;
            right: 35px;
            animation-delay: 1.5s;
        }

        /* 微粒子效果 */
        .particle-system {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            width: 3px;
            height: 3px;
            background: var(--color-neon-magenta);
            border-radius: 50%;
            animation: particle-float 6s ease-in-out infinite;
        }

        .particle-1 {
            top: 20%;
            left: 15%;
            animation-delay: 0s;
        }

        .particle-2 {
            top: 30%;
            right: 20%;
            animation-delay: 1s;
        }

        .particle-3 {
            bottom: 25%;
            left: 25%;
            animation-delay: 2s;
        }

        .particle-4 {
            bottom: 35%;
            right: 15%;
            animation-delay: 3s;
        }

        /* 動畫定義 */
        @keyframes sphere-wave-3d {
            0%, 100% {
                transform: translateZ(var(--layer-z, 0px)) scale(var(--layer-scale, 1)) rotateY(0deg);
                opacity: 0.6;
            }
            25% {
                transform: translateZ(var(--layer-z, 0px)) scale(calc(var(--layer-scale, 1) * 1.1)) rotateY(90deg);
                opacity: 0.8;
            }
            50% {
                transform: translateZ(var(--layer-z, 0px)) scale(var(--layer-scale, 1)) rotateY(180deg);
                opacity: 0.6;
            }
            75% {
                transform: translateZ(var(--layer-z, 0px)) scale(calc(var(--layer-scale, 1) * 0.9)) rotateY(270deg);
                opacity: 0.4;
            }
        }

        @keyframes core-pulse {
            0%, 100% {
                transform: translate(-50%, -50%) scale(1);
                box-shadow: 0 0 20px var(--color-primary);
            }
            50% {
                transform: translate(-50%, -50%) scale(1.1);
                box-shadow: 0 0 40px var(--color-primary);
            }
        }

        @keyframes gear-rotate-cw {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes gear-rotate-ccw {
            from { transform: rotate(0deg); }
            to { transform: rotate(-360deg); }
        }

        @keyframes data-flow {
            0% {
                opacity: 0;
                transform: translateY(0px);
            }
            50% {
                opacity: 1;
                transform: translateY(-10px);
            }
            100% {
                opacity: 0;
                transform: translateY(-20px);
            }
        }

        @keyframes particle-float {
            0%, 100% {
                transform: translateY(0px) scale(1);
                opacity: 0.3;
            }
            25% {
                transform: translateY(-8px) scale(1.2);
                opacity: 0.7;
            }
            50% {
                transform: translateY(-4px) scale(1);
                opacity: 1;
            }
            75% {
                transform: translateY(-12px) scale(0.8);
                opacity: 0.5;
            }
        }

        .comparison-info {
            position: absolute;
            bottom: 40px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            color: var(--color-neon-yellow);
            font-size: 14px;
            line-height: 1.6;
        }

        .version-badge {
            display: inline-block;
            padding: 4px 8px;
            margin: 0 5px;
            border: 1px solid var(--color-neon-cyan);
            border-radius: 4px;
            background: rgba(0, 255, 255, 0.1);
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <!-- 舊版 3D 水母 -->
        <div class="jellyfish-section">
            <div class="section-title">
                <span class="version-badge">舊版</span>
                3D 透明水母霓虹燈
            </div>
            <div class="jellyfish-3d">
                <div class="jellyfish-sphere-3d">
                    <div class="sphere-layer-3d layer-1-3d"></div>
                    <div class="sphere-layer-3d layer-2-3d"></div>
                    <div class="sphere-layer-3d layer-3-3d"></div>
                    <div class="sphere-layer-3d layer-4-3d"></div>
                </div>
            </div>
        </div>

        <!-- 新版精密機械水母 -->
        <div class="jellyfish-section">
            <div class="section-title">
                <span class="version-badge">新版</span>
                精密內部零件運轉感
            </div>
            <div class="jellyfish-mechanical">
                <div class="mechanical-core"></div>
                <div class="gear-system">
                    <div class="gear gear-1"></div>
                    <div class="gear gear-2"></div>
                    <div class="gear gear-3"></div>
                    <div class="gear gear-4"></div>
                </div>
                <div class="data-stream stream-1"></div>
                <div class="data-stream stream-2"></div>
                <div class="data-stream stream-3"></div>
                <div class="data-stream stream-4"></div>
                <div class="particle-system">
                    <div class="particle particle-1"></div>
                    <div class="particle particle-2"></div>
                    <div class="particle particle-3"></div>
                    <div class="particle particle-4"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="comparison-info">
        [*] 水母版本預覽引擎 | 比較舊版 3D 透明效果與新版精密機械運轉感 ^o^<br>
        [+] 左側：原始 3D 多層球體動畫 | 右側：齒輪系統 + 數據流 + 微粒子效果<br>
        [i] 點擊水母可以觸發互動效果 =__=
    </div>

    <script>
        // 簡單的互動效果
        document.querySelectorAll('.jellyfish-3d, .jellyfish-mechanical').forEach(jellyfish => {
            jellyfish.addEventListener('click', () => {
                jellyfish.style.transform = 'scale(1.2)';
                setTimeout(() => {
                    jellyfish.style.transform = 'scale(1)';
                }, 200);
            });
        });

        console.log('[*] 水母版本預覽引擎載入完成 ^o^');
        console.log('[+] 舊版特色：3D 透視、多層球體、霓虹漸變');
        console.log('[+] 新版特色：齒輪系統、數據流、微粒子效果');
        console.log('[i] 可以結合兩種風格創造更豐富的動畫效果 XD');
    </script>
</body>
</html>`);
});

export default app;
