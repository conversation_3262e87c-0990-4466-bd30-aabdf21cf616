import { Hono } from 'hono';

const app = new Hono();

// 🎭 水母版本預覽引擎 - 精密內部零件運轉感
app.get('/jellyfish-preview', (c) => {
	return c.html(`<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水母版本預覽引擎 | 精密內部零件運轉感</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        :root {
            --color-primary: #00ff41;
            --color-secondary: #0066cc;
            --color-accent: #ff6b35;
            --color-neon-cyan: #00ffff;
            --color-neon-magenta: #ff00ff;
            --color-neon-yellow: #ffff00;
            --bg-primary: #0a0a0a;
            --bg-secondary: #1a1a1a;
        }

        body {
            background: var(--bg-primary);
            color: var(--color-primary);
            font-family: 'Courier New', monospace;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .preview-container {
            display: flex;
            gap: 40px;
            align-items: center;
            justify-content: center;
            width: 100%;
            max-width: 1400px;
            padding: 40px;
            flex-wrap: wrap;
        }

        .jellyfish-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }

        .section-title {
            font-size: 18px;
            color: var(--color-neon-cyan);
            text-align: center;
            margin-bottom: 10px;
            text-shadow: 0 0 10px var(--color-neon-cyan);
        }

        /* 🐙 舊版 3D 水母動畫 */
        .jellyfish-3d {
            width: 120px;
            height: 120px;
            position: relative;
            cursor: pointer;
            perspective: 300px;
            transform-style: preserve-3d;
        }

        .jellyfish-sphere-3d {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            transform-style: preserve-3d;
        }

        .sphere-layer-3d {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            border: 1px solid transparent;
            background: radial-gradient(
                circle at 30% 30%,
                rgba(255, 0, 128, 0.3) 0%,
                rgba(0, 255, 255, 0.2) 25%,
                rgba(255, 255, 0, 0.2) 50%,
                rgba(255, 128, 0, 0.2) 75%,
                transparent 100%
            );
            animation: sphere-wave-3d 8s ease-in-out infinite;
        }

        .layer-1-3d {
            transform: translateZ(0px) scale(1);
            background: radial-gradient(
                circle at 30% 30%,
                rgba(255, 0, 128, 0.4) 0%,
                rgba(128, 0, 255, 0.3) 50%,
                transparent 100%
            );
            animation-delay: 0s;
        }

        .layer-2-3d {
            transform: translateZ(-10px) scale(0.9);
            background: radial-gradient(
                circle at 40% 40%,
                rgba(0, 255, 255, 0.3) 0%,
                rgba(0, 128, 255, 0.2) 50%,
                transparent 100%
            );
            animation-delay: 2s;
        }

        .layer-3-3d {
            transform: translateZ(-20px) scale(0.8);
            background: radial-gradient(
                circle at 50% 50%,
                rgba(255, 255, 0, 0.3) 0%,
                rgba(255, 128, 0, 0.2) 50%,
                transparent 100%
            );
            animation-delay: 4s;
        }

        .layer-4-3d {
            transform: translateZ(-30px) scale(0.7);
            background: radial-gradient(
                circle at 60% 60%,
                rgba(255, 128, 0, 0.3) 0%,
                rgba(255, 0, 128, 0.2) 50%,
                transparent 100%
            );
            animation-delay: 6s;
        }

        /* 🦕 /cli-deno 版本水母動畫 */
        .jellyfish-deno {
            width: 120px;
            height: 120px;
            position: relative;
            cursor: pointer;
            animation: jellyfish-float 6s ease-in-out infinite;
        }

        .jellyfish-sphere-deno {
            position: absolute;
            border-radius: 50%;
            background: linear-gradient(135deg,
                var(--color-primary) 0%,
                var(--color-secondary) 50%,
                var(--color-accent) 100%);
            border: 1px solid var(--color-primary);
            animation: jellyfish-pulse 4s ease-in-out infinite;
        }

        .jellyfish-sphere-deno-1 {
            width: 80px;
            height: 80px;
            top: 20px;
            left: 20px;
            animation-delay: 0s;
        }

        .jellyfish-sphere-deno-2 {
            width: 60px;
            height: 60px;
            top: 30px;
            left: 30px;
            opacity: 0.8;
            animation-delay: 1.3s;
        }

        .jellyfish-sphere-deno-3 {
            width: 40px;
            height: 40px;
            top: 40px;
            left: 40px;
            opacity: 0.6;
            animation-delay: 2.6s;
        }

        .jellyfish-core-deno {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            transform: translate(-50%, -50%);
            border-radius: 50%;
            background: var(--color-neon-cyan);
            animation: jellyfish-core-glow 3s ease-in-out infinite;
        }

        .jellyfish-tentacles-deno {
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 40px;
        }

        .tentacle-deno {
            position: absolute;
            width: 2px;
            background: linear-gradient(to bottom, var(--color-primary), transparent);
            border-radius: 1px;
            animation: tentacle-sway 4s ease-in-out infinite;
        }

        .tentacle-deno-1 {
            height: 30px;
            left: 20%;
            animation-delay: 0s;
        }

        .tentacle-deno-2 {
            height: 25px;
            left: 35%;
            animation-delay: 0.5s;
        }

        .tentacle-deno-3 {
            height: 35px;
            left: 50%;
            animation-delay: 1s;
        }

        .tentacle-deno-4 {
            height: 28px;
            left: 65%;
            animation-delay: 1.5s;
        }

        .tentacle-deno-5 {
            height: 32px;
            left: 80%;
            animation-delay: 2s;
        }

        /* 🕰️ 陀飛輪風格水母設計 */
        .jellyfish-tourbillon {
            width: 120px;
            height: 120px;
            position: relative;
            cursor: pointer;
            perspective: 400px;
            transform-style: preserve-3d;
        }

        /* 外框 - 陀飛輪籠架 */
        .tourbillon-cage {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 100px;
            height: 100px;
            transform: translate(-50%, -50%);
            border: 2px solid var(--color-neon-cyan);
            border-radius: 50%;
            animation: tourbillon-cage-rotate 60s linear infinite;
        }

        /* 內部旋轉框架 */
        .tourbillon-frame {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 80px;
            height: 80px;
            transform: translate(-50%, -50%);
            border: 1px solid var(--color-neon-yellow);
            border-radius: 50%;
            animation: tourbillon-frame-rotate 30s linear infinite reverse;
        }

        /* 中央擺輪 */
        .tourbillon-balance {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 40px;
            height: 40px;
            transform: translate(-50%, -50%);
            border-radius: 50%;
            background: radial-gradient(
                circle at center,
                var(--color-neon-magenta) 0%,
                rgba(255, 0, 255, 0.6) 30%,
                rgba(255, 0, 255, 0.2) 70%,
                transparent 100%
            );
            border: 1px solid var(--color-neon-magenta);
            animation: tourbillon-balance-oscillate 2s ease-in-out infinite;
        }

        /* 擺輪指針 */
        .balance-pointer {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 2px;
            height: 15px;
            background: var(--color-neon-yellow);
            transform-origin: bottom center;
            transform: translate(-50%, -100%);
            animation: balance-pointer-swing 2s ease-in-out infinite;
        }

        /* 齒輪系統 - 更精密的陀飛輪齒輪 */
        .tourbillon-gear {
            position: absolute;
            border-radius: 50%;
            border: 1px solid var(--color-neon-cyan);
            background: radial-gradient(
                circle at center,
                rgba(0, 255, 255, 0.3) 0%,
                rgba(0, 255, 255, 0.1) 50%,
                transparent 100%
            );
        }

        /* 齒輪齒紋效果 */
        .tourbillon-gear::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border-radius: 50%;
            background: conic-gradient(
                from 0deg,
                transparent 0deg,
                var(--color-neon-cyan) 5deg,
                transparent 10deg,
                var(--color-neon-cyan) 15deg,
                transparent 20deg,
                var(--color-neon-cyan) 25deg,
                transparent 30deg,
                var(--color-neon-cyan) 35deg,
                transparent 40deg,
                var(--color-neon-cyan) 45deg,
                transparent 50deg,
                var(--color-neon-cyan) 55deg,
                transparent 60deg
            );
            opacity: 0.6;
        }

        .tourbillon-gear-1 {
            width: 25px;
            height: 25px;
            top: 10px;
            left: 30px;
            animation: tourbillon-gear-rotate-fast 3s linear infinite;
        }

        .tourbillon-gear-2 {
            width: 20px;
            height: 20px;
            top: 15px;
            right: 25px;
            animation: tourbillon-gear-rotate-medium 4s linear infinite reverse;
        }

        .tourbillon-gear-3 {
            width: 18px;
            height: 18px;
            bottom: 20px;
            left: 25px;
            animation: tourbillon-gear-rotate-slow 6s linear infinite;
        }

        .tourbillon-gear-4 {
            width: 22px;
            height: 22px;
            bottom: 15px;
            right: 30px;
            animation: tourbillon-gear-rotate-fast 2.5s linear infinite reverse;
        }

        /* 發條盒 */
        .mainspring-barrel {
            position: absolute;
            top: 5px;
            left: 50%;
            width: 15px;
            height: 15px;
            transform: translateX(-50%);
            border-radius: 50%;
            background: var(--color-neon-yellow);
            animation: mainspring-pulse 4s ease-in-out infinite;
        }

        /* 擒縱機構 */
        .escapement {
            position: absolute;
            bottom: 5px;
            left: 50%;
            width: 12px;
            height: 8px;
            transform: translateX(-50%);
            background: var(--color-neon-magenta);
            clip-path: polygon(0% 0%, 100% 0%, 80% 100%, 20% 100%);
            animation: escapement-tick 1s ease-in-out infinite;
        }

        .gear-system {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .gear {
            position: absolute;
            border-radius: 50%;
            border: 2px solid var(--color-neon-cyan);
            background: radial-gradient(
                circle at center,
                rgba(0, 255, 255, 0.2) 0%,
                rgba(0, 255, 255, 0.1) 50%,
                transparent 100%
            );
        }

        .gear-1 {
            width: 40px;
            height: 40px;
            top: -20px;
            left: -20px;
            animation: gear-rotate-cw 4s linear infinite;
        }

        .gear-2 {
            width: 30px;
            height: 30px;
            top: -15px;
            right: -35px;
            animation: gear-rotate-ccw 3s linear infinite;
        }

        .gear-3 {
            width: 25px;
            height: 25px;
            bottom: -25px;
            left: -12px;
            animation: gear-rotate-cw 5s linear infinite;
        }

        .gear-4 {
            width: 35px;
            height: 35px;
            bottom: -18px;
            right: -30px;
            animation: gear-rotate-ccw 2.5s linear infinite;
        }

        /* 數據流動畫 */
        .data-stream {
            position: absolute;
            width: 2px;
            height: 20px;
            background: linear-gradient(
                to bottom,
                var(--color-neon-yellow),
                transparent
            );
            animation: data-flow 2s ease-in-out infinite;
        }

        .stream-1 {
            top: 10px;
            left: 30px;
            animation-delay: 0s;
        }

        .stream-2 {
            top: 20px;
            right: 25px;
            animation-delay: 0.5s;
        }

        .stream-3 {
            bottom: 15px;
            left: 40px;
            animation-delay: 1s;
        }

        .stream-4 {
            bottom: 25px;
            right: 35px;
            animation-delay: 1.5s;
        }

        /* 微粒子效果 */
        .particle-system {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            width: 3px;
            height: 3px;
            background: var(--color-neon-magenta);
            border-radius: 50%;
            animation: particle-float 6s ease-in-out infinite;
        }

        .particle-1 {
            top: 20%;
            left: 15%;
            animation-delay: 0s;
        }

        .particle-2 {
            top: 30%;
            right: 20%;
            animation-delay: 1s;
        }

        .particle-3 {
            bottom: 25%;
            left: 25%;
            animation-delay: 2s;
        }

        .particle-4 {
            bottom: 35%;
            right: 15%;
            animation-delay: 3s;
        }

        /* 動畫定義 */
        @keyframes sphere-wave-3d {
            0%, 100% {
                transform: translateZ(var(--layer-z, 0px)) scale(var(--layer-scale, 1)) rotateY(0deg);
                opacity: 0.6;
            }
            25% {
                transform: translateZ(var(--layer-z, 0px)) scale(calc(var(--layer-scale, 1) * 1.1)) rotateY(90deg);
                opacity: 0.8;
            }
            50% {
                transform: translateZ(var(--layer-z, 0px)) scale(var(--layer-scale, 1)) rotateY(180deg);
                opacity: 0.6;
            }
            75% {
                transform: translateZ(var(--layer-z, 0px)) scale(calc(var(--layer-scale, 1) * 0.9)) rotateY(270deg);
                opacity: 0.4;
            }
        }

        @keyframes core-pulse {
            0%, 100% {
                transform: translate(-50%, -50%) scale(1);
                box-shadow: 0 0 20px var(--color-primary);
            }
            50% {
                transform: translate(-50%, -50%) scale(1.1);
                box-shadow: 0 0 40px var(--color-primary);
            }
        }

        @keyframes gear-rotate-cw {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes gear-rotate-ccw {
            from { transform: rotate(0deg); }
            to { transform: rotate(-360deg); }
        }

        @keyframes data-flow {
            0% {
                opacity: 0;
                transform: translateY(0px);
            }
            50% {
                opacity: 1;
                transform: translateY(-10px);
            }
            100% {
                opacity: 0;
                transform: translateY(-20px);
            }
        }

        @keyframes particle-float {
            0%, 100% {
                transform: translateY(0px) scale(1);
                opacity: 0.3;
            }
            25% {
                transform: translateY(-8px) scale(1.2);
                opacity: 0.7;
            }
            50% {
                transform: translateY(-4px) scale(1);
                opacity: 1;
            }
            75% {
                transform: translateY(-12px) scale(0.8);
                opacity: 0.5;
            }
        }

        /* /cli-deno 水母動畫關鍵幀 */
        @keyframes jellyfish-float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            25% {
                transform: translateY(-10px) rotate(2deg);
            }
            50% {
                transform: translateY(-5px) rotate(0deg);
            }
            75% {
                transform: translateY(-15px) rotate(-2deg);
            }
        }

        @keyframes jellyfish-pulse {
            0%, 100% {
                transform: scale(1);
                opacity: 0.8;
            }
            50% {
                transform: scale(1.1);
                opacity: 1;
            }
        }

        @keyframes jellyfish-core-glow {
            0%, 100% {
                box-shadow: 0 0 12px var(--color-neon-cyan);
                transform: translate(-50%, -50%) scale(1);
            }
            50% {
                box-shadow: 0 0 20px var(--color-neon-cyan);
                transform: translate(-50%, -50%) scale(1.2);
            }
        }

        @keyframes tentacle-sway {
            0%, 100% {
                transform: rotate(0deg);
            }
            25% {
                transform: rotate(5deg);
            }
            75% {
                transform: rotate(-5deg);
            }
        }

        /* 陀飛輪動畫關鍵幀 */
        @keyframes tourbillon-cage-rotate {
            from { transform: translate(-50%, -50%) rotate(0deg); }
            to { transform: translate(-50%, -50%) rotate(360deg); }
        }

        @keyframes tourbillon-frame-rotate {
            from { transform: translate(-50%, -50%) rotate(0deg); }
            to { transform: translate(-50%, -50%) rotate(360deg); }
        }

        @keyframes tourbillon-balance-oscillate {
            0%, 100% {
                transform: translate(-50%, -50%) scale(1) rotate(0deg);
                box-shadow: 0 0 10px var(--color-neon-magenta);
            }
            25% {
                transform: translate(-50%, -50%) scale(1.1) rotate(5deg);
                box-shadow: 0 0 15px var(--color-neon-magenta);
            }
            50% {
                transform: translate(-50%, -50%) scale(1) rotate(0deg);
                box-shadow: 0 0 20px var(--color-neon-magenta);
            }
            75% {
                transform: translate(-50%, -50%) scale(1.1) rotate(-5deg);
                box-shadow: 0 0 15px var(--color-neon-magenta);
            }
        }

        @keyframes balance-pointer-swing {
            0%, 100% {
                transform: translate(-50%, -100%) rotate(0deg);
            }
            25% {
                transform: translate(-50%, -100%) rotate(15deg);
            }
            75% {
                transform: translate(-50%, -100%) rotate(-15deg);
            }
        }

        @keyframes tourbillon-gear-rotate-fast {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes tourbillon-gear-rotate-medium {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes tourbillon-gear-rotate-slow {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes mainspring-pulse {
            0%, 100% {
                transform: translateX(-50%) scale(1);
                box-shadow: 0 0 8px var(--color-neon-yellow);
            }
            50% {
                transform: translateX(-50%) scale(1.2);
                box-shadow: 0 0 15px var(--color-neon-yellow);
            }
        }

        @keyframes escapement-tick {
            0%, 50% {
                transform: translateX(-50%) scaleX(1);
                opacity: 1;
            }
            25%, 75% {
                transform: translateX(-50%) scaleX(1.3);
                opacity: 0.8;
            }
        }

        .comparison-info {
            position: absolute;
            bottom: 40px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            color: var(--color-neon-yellow);
            font-size: 14px;
            line-height: 1.6;
        }

        .version-badge {
            display: inline-block;
            padding: 4px 8px;
            margin: 0 5px;
            border: 1px solid var(--color-neon-cyan);
            border-radius: 4px;
            background: rgba(0, 255, 255, 0.1);
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <!-- 舊版 3D 水母 -->
        <div class="jellyfish-section">
            <div class="section-title">
                <span class="version-badge">舊版</span>
                3D 透明水母霓虹燈
            </div>
            <div class="jellyfish-3d">
                <div class="jellyfish-sphere-3d">
                    <div class="sphere-layer-3d layer-1-3d"></div>
                    <div class="sphere-layer-3d layer-2-3d"></div>
                    <div class="sphere-layer-3d layer-3-3d"></div>
                    <div class="sphere-layer-3d layer-4-3d"></div>
                </div>
            </div>
        </div>

        <!-- /cli-deno 版本水母 -->
        <div class="jellyfish-section">
            <div class="section-title">
                <span class="version-badge">/cli-deno</span>
                多層球體 + 觸手動畫
            </div>
            <div class="jellyfish-deno">
                <div class="jellyfish-sphere-deno jellyfish-sphere-deno-1"></div>
                <div class="jellyfish-sphere-deno jellyfish-sphere-deno-2"></div>
                <div class="jellyfish-sphere-deno jellyfish-sphere-deno-3"></div>
                <div class="jellyfish-core-deno"></div>
                <div class="jellyfish-tentacles-deno">
                    <div class="tentacle-deno tentacle-deno-1"></div>
                    <div class="tentacle-deno tentacle-deno-2"></div>
                    <div class="tentacle-deno tentacle-deno-3"></div>
                    <div class="tentacle-deno tentacle-deno-4"></div>
                    <div class="tentacle-deno tentacle-deno-5"></div>
                </div>
            </div>
        </div>

        <!-- 陀飛輪風格水母 -->
        <div class="jellyfish-section">
            <div class="section-title">
                <span class="version-badge">陀飛輪</span>
                瑞士製錶工藝美學
            </div>
            <div class="jellyfish-tourbillon">
                <!-- 陀飛輪籠架 -->
                <div class="tourbillon-cage"></div>

                <!-- 內部旋轉框架 -->
                <div class="tourbillon-frame"></div>

                <!-- 中央擺輪 -->
                <div class="tourbillon-balance">
                    <div class="balance-pointer"></div>
                </div>

                <!-- 精密齒輪系統 -->
                <div class="tourbillon-gear tourbillon-gear-1"></div>
                <div class="tourbillon-gear tourbillon-gear-2"></div>
                <div class="tourbillon-gear tourbillon-gear-3"></div>
                <div class="tourbillon-gear tourbillon-gear-4"></div>

                <!-- 發條盒 -->
                <div class="mainspring-barrel"></div>

                <!-- 擒縱機構 -->
                <div class="escapement"></div>
            </div>
        </div>
    </div>

    <div class="comparison-info">
        [*] 水母版本預覽引擎 | 三種不同風格的水母動畫比較 ^o^<br>
        [+] 左：原始 3D 透明多層 | 中：/cli-deno 球體觸手 | 右：陀飛輪製錶工藝<br>
        [i] 點擊水母可以觸發互動效果 =__= | 陀飛輪版本展現精密機械美學 ㄎㄎ
    </div>

    <script>
        // 簡單的互動效果
        document.querySelectorAll('.jellyfish-3d, .jellyfish-deno, .jellyfish-tourbillon').forEach(jellyfish => {
            jellyfish.addEventListener('click', () => {
                jellyfish.style.transform = 'scale(1.2)';
                setTimeout(() => {
                    jellyfish.style.transform = 'scale(1)';
                }, 200);
            });
        });

        console.log('[*] 水母版本預覽引擎載入完成 ^o^');
        console.log('[+] 舊版特色：3D 透視、多層球體、霓虹漸變');
        console.log('[+] /cli-deno 特色：多層球體、觸手動畫、核心發光');
        console.log('[+] 陀飛輪特色：籠架旋轉、擺輪振盪、擒縱機構');
        console.log('[i] 陀飛輪版本展現瑞士製錶工藝的精密美學 =__=');
    </script>
</body>
</html>`);
});

export default app;
