import { Hono } from 'hono';

const app = new Hono();

// 🦕 Deno 聊天 API - 為 CLI Preview Deno 版本提供 AI 聊天服務
// 這個 API 提供了與 Deno 兼容的聊天接口，但在 Node.js 環境中運行

// 🔧 健康檢查端點
app.get('/health', (c) => {
    return c.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'Deno Chat API Adapter',
        version: '1.0.0',
        environment: 'Node.js (Deno Compatible)',
        ai_providers: {
            openrouter: process.env.OPENROUTER_API_KEY ? '✅ 已配置' : '❌ 未配置',
            claude: process.env.CLAUDE_API_KEY ? '✅ 已配置' : '❌ 未配置'
        }
    });
});

// 🤖 AI 聊天端點
app.post('/chat', async (c) => {
    try {
        const startTime = Date.now();
        const { 
            message, 
            provider = 'openrouter',
            model = 'openai/gpt-4o-mini',
            conversationHistory = [],
            systemPrompt
        } = await c.req.json();

        if (!message) {
            return c.json({
                success: false,
                error: 'Message is required'
            }, 400);
        }

        console.log(`🤖 Deno Chat API 處理請求 (${provider}/${model}):`, message.substring(0, 50) + '...');

        // 構建對話上下文
        const messages = [
            {
                role: 'system',
                content: systemPrompt || getDefaultSystemPrompt()
            },
            // 添加對話歷史 (最近 8 條)
            ...conversationHistory.slice(-8).map(msg => ({
                role: msg.type === 'user' ? 'user' : 'assistant',
                content: msg.content
            })),
            {
                role: 'user',
                content: message
            }
        ];

        let response;
        let usedProvider = provider;

        try {
            // 嘗試使用指定的提供商
            if (provider === 'openrouter') {
                response = await callOpenRouter(model, messages);
            } else if (provider === 'claude') {
                response = await callClaude(model, messages);
            } else {
                throw new Error(`不支持的提供商: ${provider}`);
            }
        } catch (error) {
            console.warn(`⚠️ ${provider} 調用失敗，嘗試降級...`, error.message);
            
            // 降級策略：OpenRouter -> Claude -> 模擬回應
            if (provider !== 'openrouter' && process.env.OPENROUTER_API_KEY) {
                try {
                    response = await callOpenRouter('openai/gpt-4o-mini', messages);
                    usedProvider = 'openrouter (fallback)';
                } catch (fallbackError) {
                    console.warn('⚠️ OpenRouter 降級也失敗:', fallbackError.message);
                    response = generateMockResponse(message);
                    usedProvider = 'mock (fallback)';
                }
            } else if (provider !== 'claude' && process.env.CLAUDE_API_KEY) {
                try {
                    response = await callClaude('claude-3-haiku-20240307', messages);
                    usedProvider = 'claude (fallback)';
                } catch (fallbackError) {
                    console.warn('⚠️ Claude 降級也失敗:', fallbackError.message);
                    response = generateMockResponse(message);
                    usedProvider = 'mock (fallback)';
                }
            } else {
                response = generateMockResponse(message);
                usedProvider = 'mock (fallback)';
            }
        }

        const endTime = Date.now();
        const responseTime = endTime - startTime;

        console.log(`✅ Deno Chat API 完成，響應時間: ${responseTime}ms, 提供商: ${usedProvider}`);

        return c.json({
            success: true,
            response: response,
            provider: usedProvider,
            model: model,
            timestamp: endTime,
            performance_ms: responseTime
        });

    } catch (error) {
        console.error('❌ Deno Chat API 錯誤:', error);
        return c.json({
            success: false,
            error: 'Chat API failed',
            message: error.message
        }, 500);
    }
});

// 📋 獲取可用模型列表
app.get('/models', (c) => {
    const models = {
        openrouter: {
            'openai/gpt-4o-mini': {
                name: 'GPT-4o Mini',
                provider: 'openrouter',
                status: process.env.OPENROUTER_API_KEY ? 'available' : 'unavailable'
            },
            'anthropic/claude-3.5-sonnet': {
                name: 'Claude 3.5 Sonnet',
                provider: 'openrouter',
                status: process.env.OPENROUTER_API_KEY ? 'available' : 'unavailable'
            },
            'google/gemini-2.0-flash-exp': {
                name: 'Gemini 2.0 Flash',
                provider: 'openrouter',
                status: process.env.OPENROUTER_API_KEY ? 'available' : 'unavailable'
            }
        },
        claude: {
            'claude-3-5-sonnet-20241022': {
                name: 'Claude 3.5 Sonnet',
                provider: 'claude',
                status: process.env.CLAUDE_API_KEY ? 'available' : 'unavailable'
            },
            'claude-3-haiku-20240307': {
                name: 'Claude 3 Haiku',
                provider: 'claude',
                status: process.env.CLAUDE_API_KEY ? 'available' : 'unavailable'
            }
        }
    };

    return c.json({
        success: true,
        models: models,
        default_model: 'openai/gpt-4o-mini',
        default_provider: 'openrouter'
    });
});

// 🔧 OpenRouter API 調用
async function callOpenRouter(model, messages) {
    const apiKey = process.env.OPENROUTER_API_KEY;
    if (!apiKey) {
        throw new Error('OpenRouter API Key 未配置');
    }

    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`,
            'HTTP-Referer': 'https://heartsync-v2.up.railway.app',
            'X-Title': 'HeartSync CLI Preview Deno'
        },
        body: JSON.stringify({
            model: model,
            messages: messages,
            max_tokens: 1000,
            temperature: 0.7
        })
    });

    if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`OpenRouter API 錯誤: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
}

// 🔧 Claude API 調用
async function callClaude(model, messages) {
    const apiKey = process.env.CLAUDE_API_KEY;
    if (!apiKey) {
        throw new Error('Claude API Key 未配置');
    }

    // 提取系統提示和用戶訊息
    const systemMessage = messages.find(m => m.role === 'system');
    const userMessages = messages.filter(m => m.role !== 'system');

    const response = await fetch('https://api.anthropic.com/v1/messages', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'x-api-key': apiKey,
            'anthropic-version': '2023-06-01'
        },
        body: JSON.stringify({
            model: model,
            max_tokens: 1000,
            system: systemMessage?.content || getDefaultSystemPrompt(),
            messages: userMessages
        })
    });

    if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Claude API 錯誤: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
    }

    const data = await response.json();
    return data.content[0].text;
}

// 🔧 生成模擬回應 (降級使用)
function generateMockResponse(message) {
    const responses = [
        `我收到了您的訊息："${message.substring(0, 50)}..."。這是一個模擬回應，因為 AI API 暫時不可用。`,
        `感謝您的訊息！目前 AI 服務正在維護中，這是一個自動回應。您的訊息已記錄。`,
        `🦕 Deno 聊天系統收到您的訊息。由於 API 限制，目前提供模擬回應。請稍後再試。`,
        `您好！我是 HSN CLI Preview Deno 版本的 AI 助手。目前使用模擬模式回應您的問題。`
    ];
    
    return responses[Math.floor(Math.random() * responses.length)];
}

// 🔧 獲取預設系統提示
function getDefaultSystemPrompt() {
    return `你是 HSN (HeartSync Network) 的 AI 知識探索夥伴，現在運行在 Deno + Datastar 技術棧上。你的任務是：

1. * **知識探索**: 幫助用戶深入探索各種主題和概念，運用最新的推理能力
2. + **友善對話**: 保持溫暖、友善且富有洞察力的對話風格
3. = **簡潔回應**: 提供有價值但簡潔的回應，避免過長的解釋
4. | **啟發思考**: 鼓勵用戶進一步思考和探索
5. - **前沿技術**: 運用 2025 年最新的 Deno + Datastar 技術為用戶提供更好的體驗

請用繁體中文回應，保持專業但親切的語調。重要：完全避免使用現代表情符號，只使用復古終端符號如 * + - = | 等。`;
}

export default app;
