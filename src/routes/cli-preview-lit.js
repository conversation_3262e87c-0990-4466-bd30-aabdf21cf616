// 在現有 Alpine.js 中整合 Lit 組件
app.get('/cli-preview', (c) => {
  return c.html(`<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <!-- 現有的 meta 標籤 -->
    
    <!-- Lit 核心 -->
    <script type="module" src="https://cdn.skypack.dev/lit@3.0"></script>
    
    <!-- 自定義組件 -->
    <script type="module" src="/components/terminal-app.js"></script>
    <script type="module" src="/components/chat-message.js"></script>
    
    <!-- Alpine.js (保持現有) -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.14.7/dist/cdn.min.js"></script>
</head>
<body>
    <!-- Alpine.js 數據管理 + Lit 組件 UI -->
    <div x-data="hsnApp()">
        <hsn-terminal-app 
            :theme="currentTheme"
            :is-connected="isConnected"
            :ai-provider="aiProvider"
            @message-send="handleMessageSend($event)"
            @theme-change="handleThemeChange($event)">
        </hsn-terminal-app>
    </div>

    <script>
        function hsnApp() {
            return {
                currentTheme: 'enhanced-phosphor-green',
                isConnected: false,
                aiProvider: 'openrouter',
                
                handleMessageSend(event) {
                    // Alpine.js 處理邏輯
                    this.sendMessage(event.detail.message);
                },
                
                handleThemeChange(event) {
                    this.currentTheme = event.detail.theme;
                }
            };
        }
    </script>
</body>
</html>`);
});