import { Hono } from 'hono';

const app = new Hono();

// 🐱 貓用人工智慧模式 - 實驗性功能
app.get('/cat-ai-mode', (c) => {
    return c.html(`<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>貓用人工智慧模式 | Cat AI Mode</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        :root {
            --cat-primary: #ff6b9d;
            --cat-secondary: #4ecdc4;
            --cat-accent: #ffe66d;
            --cat-bg: #2d1b69;
            --cat-surface: #3a2a7a;
            --cat-text: #ffffff;
            --cat-paw: #ff8fab;
        }

        body {
            background: linear-gradient(135deg, var(--cat-bg) 0%, var(--cat-surface) 100%);
            color: var(--cat-text);
            font-family: 'Comic Sans MS', cursive, sans-serif;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            overflow-x: hidden;
        }

        .cat-container {
            max-width: 800px;
            width: 100%;
            padding: 20px;
            text-align: center;
        }

        .cat-title {
            font-size: 2.5rem;
            color: var(--cat-primary);
            margin-bottom: 10px;
            text-shadow: 0 0 20px var(--cat-primary);
            animation: cat-title-glow 3s ease-in-out infinite;
        }

        .cat-subtitle {
            font-size: 1.2rem;
            color: var(--cat-secondary);
            margin-bottom: 30px;
            opacity: 0.8;
        }

        /* 🐱 貓咪頭像動畫 */
        .cat-avatar {
            width: 150px;
            height: 150px;
            margin: 0 auto 30px;
            position: relative;
            cursor: pointer;
            animation: cat-float 4s ease-in-out infinite;
        }

        .cat-face {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--cat-primary) 0%, var(--cat-paw) 100%);
            border-radius: 50% 50% 45% 45%;
            position: relative;
            border: 3px solid var(--cat-accent);
            box-shadow: 0 0 30px rgba(255, 107, 157, 0.5);
        }

        .cat-ears {
            position: absolute;
            top: -20px;
            width: 100%;
            height: 40px;
        }

        .cat-ear {
            position: absolute;
            width: 30px;
            height: 40px;
            background: var(--cat-primary);
            border: 2px solid var(--cat-accent);
            clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
        }

        .cat-ear-left {
            left: 20px;
            transform: rotate(-15deg);
            animation: ear-twitch-left 3s ease-in-out infinite;
        }

        .cat-ear-right {
            right: 20px;
            transform: rotate(15deg);
            animation: ear-twitch-right 3s ease-in-out infinite;
        }

        .cat-eyes {
            position: absolute;
            top: 40px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 20px;
        }

        .cat-eye {
            width: 20px;
            height: 25px;
            background: var(--cat-accent);
            border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
            position: relative;
            animation: cat-blink 4s ease-in-out infinite;
        }

        .cat-pupil {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 8px;
            height: 15px;
            background: #000;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            animation: pupil-dilate 5s ease-in-out infinite;
        }

        .cat-nose {
            position: absolute;
            top: 65px;
            left: 50%;
            transform: translateX(-50%);
            width: 12px;
            height: 8px;
            background: var(--cat-paw);
            border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
            border: 1px solid var(--cat-accent);
        }

        .cat-mouth {
            position: absolute;
            top: 75px;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 15px;
            border: 2px solid var(--cat-accent);
            border-top: none;
            border-radius: 0 0 50% 50%;
        }

        .cat-whiskers {
            position: absolute;
            top: 60px;
            width: 100%;
            height: 20px;
        }

        .whisker {
            position: absolute;
            width: 40px;
            height: 2px;
            background: var(--cat-accent);
            border-radius: 1px;
            animation: whisker-twitch 2s ease-in-out infinite;
        }

        .whisker-left-1 {
            left: -15px;
            top: 5px;
            transform: rotate(-10deg);
            animation-delay: 0s;
        }

        .whisker-left-2 {
            left: -10px;
            top: 15px;
            transform: rotate(-5deg);
            animation-delay: 0.5s;
        }

        .whisker-right-1 {
            right: -15px;
            top: 5px;
            transform: rotate(10deg);
            animation-delay: 1s;
        }

        .whisker-right-2 {
            right: -10px;
            top: 15px;
            transform: rotate(5deg);
            animation-delay: 1.5s;
        }

        /* 🎮 貓用介面控制 */
        .cat-controls {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-top: 30px;
        }

        .cat-mode-selector {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .cat-mode-btn {
            padding: 12px 20px;
            background: var(--cat-surface);
            border: 2px solid var(--cat-secondary);
            border-radius: 25px;
            color: var(--cat-text);
            font-family: inherit;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .cat-mode-btn:hover {
            background: var(--cat-secondary);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(78, 205, 196, 0.3);
        }

        .cat-mode-btn.active {
            background: var(--cat-primary);
            border-color: var(--cat-primary);
            animation: paw-glow 2s ease-in-out infinite;
        }

        .cat-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .cat-feature {
            background: var(--cat-surface);
            border: 2px solid var(--cat-secondary);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .cat-feature:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(78, 205, 196, 0.2);
        }

        .cat-feature-icon {
            font-size: 2rem;
            margin-bottom: 10px;
            display: block;
        }

        .cat-feature-title {
            font-size: 1.1rem;
            color: var(--cat-primary);
            margin-bottom: 8px;
        }

        .cat-feature-desc {
            font-size: 0.9rem;
            opacity: 0.8;
            line-height: 1.4;
        }

        /* 🐾 貓爪印效果 */
        .paw-prints {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .paw-print {
            position: absolute;
            width: 20px;
            height: 20px;
            background: var(--cat-paw);
            border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
            opacity: 0.1;
            animation: paw-fade 8s ease-in-out infinite;
        }

        /* 動畫定義 */
        @keyframes cat-title-glow {
            0%, 100% { text-shadow: 0 0 20px var(--cat-primary); }
            50% { text-shadow: 0 0 30px var(--cat-primary), 0 0 40px var(--cat-paw); }
        }

        @keyframes cat-float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            25% { transform: translateY(-10px) rotate(1deg); }
            50% { transform: translateY(-5px) rotate(0deg); }
            75% { transform: translateY(-15px) rotate(-1deg); }
        }

        @keyframes ear-twitch-left {
            0%, 90%, 100% { transform: rotate(-15deg); }
            95% { transform: rotate(-25deg); }
        }

        @keyframes ear-twitch-right {
            0%, 90%, 100% { transform: rotate(15deg); }
            95% { transform: rotate(25deg); }
        }

        @keyframes cat-blink {
            0%, 90%, 100% { transform: scaleY(1); }
            95% { transform: scaleY(0.1); }
        }

        @keyframes pupil-dilate {
            0%, 100% { width: 8px; height: 15px; }
            50% { width: 12px; height: 18px; }
        }

        @keyframes whisker-twitch {
            0%, 95%, 100% { transform: rotate(var(--whisker-angle, 0deg)); }
            97% { transform: rotate(calc(var(--whisker-angle, 0deg) + 5deg)); }
        }

        @keyframes paw-glow {
            0%, 100% { box-shadow: 0 0 10px var(--cat-primary); }
            50% { box-shadow: 0 0 20px var(--cat-primary), 0 0 30px var(--cat-paw); }
        }

        @keyframes paw-fade {
            0%, 100% { opacity: 0; transform: scale(0.5); }
            50% { opacity: 0.2; transform: scale(1); }
        }

        .status-display {
            margin-top: 20px;
            padding: 15px;
            background: var(--cat-surface);
            border-radius: 10px;
            border: 1px solid var(--cat-secondary);
            font-family: 'Courier New', monospace;
            text-align: left;
        }
    </style>
</head>
<body>
    <!-- 🐾 背景貓爪印 -->
    <div class="paw-prints" id="pawPrints"></div>

    <div class="cat-container">
        <h1 class="cat-title">🐱 貓用人工智慧模式</h1>
        <p class="cat-subtitle">Cat AI Mode - 為貓咪量身打造的 AI 體驗 ฅ^•ω•^ฅ</p>

        <!-- 🐱 貓咪頭像 -->
        <div class="cat-avatar" onclick="meow()">
            <div class="cat-face">
                <div class="cat-ears">
                    <div class="cat-ear cat-ear-left"></div>
                    <div class="cat-ear cat-ear-right"></div>
                </div>
                <div class="cat-eyes">
                    <div class="cat-eye">
                        <div class="cat-pupil"></div>
                    </div>
                    <div class="cat-eye">
                        <div class="cat-pupil"></div>
                    </div>
                </div>
                <div class="cat-nose"></div>
                <div class="cat-mouth"></div>
                <div class="cat-whiskers">
                    <div class="whisker whisker-left-1" style="--whisker-angle: -10deg;"></div>
                    <div class="whisker whisker-left-2" style="--whisker-angle: -5deg;"></div>
                    <div class="whisker whisker-right-1" style="--whisker-angle: 10deg;"></div>
                    <div class="whisker whisker-right-2" style="--whisker-angle: 5deg;"></div>
                </div>
            </div>
        </div>

        <!-- 🎮 貓用模式選擇 -->
        <div class="cat-controls">
            <div class="cat-mode-selector">
                <button class="cat-mode-btn active" data-mode="playful">🎾 玩耍模式</button>
                <button class="cat-mode-btn" data-mode="sleepy">😴 午睡模式</button>
                <button class="cat-mode-btn" data-mode="hunting">🐭 狩獵模式</button>
                <button class="cat-mode-btn" data-mode="curious">🔍 好奇模式</button>
                <button class="cat-mode-btn" data-mode="affectionate">💕 撒嬌模式</button>
            </div>

            <!-- 🐾 貓用功能特色 -->
            <div class="cat-features">
                <div class="cat-feature">
                    <span class="cat-feature-icon">🐟</span>
                    <div class="cat-feature-title">智能餵食提醒</div>
                    <div class="cat-feature-desc">根據貓咪的生理時鐘和活動量，智能提醒最佳餵食時間</div>
                </div>
                <div class="cat-feature">
                    <span class="cat-feature-icon">🎵</span>
                    <div class="cat-feature-title">貓語翻譯</div>
                    <div class="cat-feature-desc">分析貓咪的叫聲頻率和模式，翻譯成人類可理解的需求</div>
                </div>
                <div class="cat-feature">
                    <span class="cat-feature-icon">🌡️</span>
                    <div class="cat-feature-title">健康監測</div>
                    <div class="cat-feature-desc">監測貓咪的活動模式，及早發現健康異常</div>
                </div>
                <div class="cat-feature">
                    <span class="cat-feature-icon">🎮</span>
                    <div class="cat-feature-title">互動遊戲</div>
                    <div class="cat-feature-desc">AI 生成適合貓咪的互動遊戲和玩具推薦</div>
                </div>
            </div>

            <!-- 狀態顯示 -->
            <div class="status-display" id="statusDisplay">
                [*] 貓用 AI 模式已啟動 ^o^<br>
                [+] 當前模式：玩耍模式 🎾<br>
                [i] 點擊貓咪頭像聽貓叫聲 =__=<br>
                [cfg] 系統正在學習您家貓咪的習性... ㄏㄏ
            </div>
        </div>
    </div>

    <script>
        // 🐱 貓叫聲效果
        function meow() {
            const meowSounds = ['喵~', '喵喵~', '呼嚕嚕~', 'Meow~', '咪咪~'];
            const randomMeow = meowSounds[Math.floor(Math.random() * meowSounds.length)];
            
            // 創建臨時顯示元素
            const meowDiv = document.createElement('div');
            meowDiv.textContent = randomMeow;
            meowDiv.style.cssText = \`
                position: fixed;
                top: 30%;
                left: 50%;
                transform: translateX(-50%);
                font-size: 2rem;
                color: var(--cat-primary);
                text-shadow: 0 0 20px var(--cat-primary);
                z-index: 1000;
                pointer-events: none;
                animation: meow-float 2s ease-out forwards;
            \`;
            
            document.body.appendChild(meowDiv);
            
            // 2秒後移除
            setTimeout(() => {
                document.body.removeChild(meowDiv);
            }, 2000);
            
            console.log('🐱 ' + randomMeow);
        }

        // 🎮 模式切換
        document.querySelectorAll('.cat-mode-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                // 移除所有 active 類
                document.querySelectorAll('.cat-mode-btn').forEach(b => b.classList.remove('active'));
                // 添加到當前按鈕
                btn.classList.add('active');
                
                const mode = btn.dataset.mode;
                const modeNames = {
                    'playful': '玩耍模式 🎾',
                    'sleepy': '午睡模式 😴',
                    'hunting': '狩獵模式 🐭',
                    'curious': '好奇模式 🔍',
                    'affectionate': '撒嬌模式 💕'
                };
                
                const statusDisplay = document.getElementById('statusDisplay');
                statusDisplay.innerHTML = \`
                    [*] 貓用 AI 模式已啟動 ^o^<br>
                    [+] 當前模式：\${modeNames[mode]}<br>
                    [i] 點擊貓咪頭像聽貓叫聲 =__=<br>
                    [cfg] 正在調整為 \${modeNames[mode]} 的行為模式... ㄎㄎ
                \`;
                
                console.log('[*] 切換到：' + modeNames[mode]);
            });
        });

        // 🐾 生成隨機貓爪印
        function createPawPrint() {
            const pawPrint = document.createElement('div');
            pawPrint.className = 'paw-print';
            pawPrint.style.left = Math.random() * 100 + '%';
            pawPrint.style.top = Math.random() * 100 + '%';
            pawPrint.style.animationDelay = Math.random() * 8 + 's';
            
            document.getElementById('pawPrints').appendChild(pawPrint);
            
            // 8秒後移除
            setTimeout(() => {
                if (pawPrint.parentNode) {
                    pawPrint.parentNode.removeChild(pawPrint);
                }
            }, 8000);
        }

        // 定期生成貓爪印
        setInterval(createPawPrint, 2000);

        // 添加 meow 動畫 CSS
        const style = document.createElement('style');
        style.textContent = \`
            @keyframes meow-float {
                0% {
                    opacity: 0;
                    transform: translateX(-50%) translateY(20px) scale(0.5);
                }
                50% {
                    opacity: 1;
                    transform: translateX(-50%) translateY(-10px) scale(1.2);
                }
                100% {
                    opacity: 0;
                    transform: translateX(-50%) translateY(-40px) scale(1);
                }
            }
        \`;
        document.head.appendChild(style);

        console.log('[*] 貓用人工智慧模式載入完成 ^o^');
        console.log('[+] 特色功能：智能餵食、貓語翻譯、健康監測、互動遊戲');
        console.log('[i] 這是一個實驗性功能，為貓咪設計的 AI 體驗 =__=');
    </script>
</body>
</html>`);
});

export default app;
